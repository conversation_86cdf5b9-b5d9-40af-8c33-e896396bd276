﻿using GEN.ModelGroupExporter.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GEN.ModelGroupExporter.Services.Interfaces
{
    /// <summary>
    /// Service for advanced file management operations
    /// </summary>
    public interface IFileManagementService
    {
        /// <summary>
        /// Validates that the export directory is writable
        /// </summary>
        /// <param name="directoryPath">Directory path to validate</param>
        /// <returns>True if directory is writable</returns>
        bool ValidateDirectoryWritable(string directoryPath);

        /// <summary>
        /// Gets available disk space in the export directory
        /// </summary>
        /// <param name="directoryPath">Directory path</param>
        /// <returns>Available space in MB</returns>
        long GetAvailableDiskSpaceMB(string directoryPath);

        /// <summary>
        /// Cleans up temporary files from previous exports
        /// </summary>
        /// <param name="directoryPath">Directory to clean</param>
        void CleanupTemporaryFiles(string directoryPath);
    }
}
