using GEN.ModelGroupExporter.Models;
using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using GEN.ModelGroupExporter.Services.Interfaces;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of IFileManagementService
    /// </summary>
    public class FileManagementService : IFileManagementService
    {
        public bool ValidateDirectoryWritable(string directoryPath)
        {
            try
            {
                // Try to create a temporary file
                var tempFileName = Path.Combine(directoryPath, $"temp_{Guid.NewGuid()}.tmp");
                File.WriteAllText(tempFileName, "test");
                File.Delete(tempFileName);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public long GetAvailableDiskSpaceMB(string directoryPath)
        {
            try
            {
                var drive = new DriveInfo(Path.GetPathRoot(directoryPath));
                return drive.AvailableFreeSpace / (1024 * 1024); // Convert to MB
            }
            catch
            {
                return -1; // Unable to determine
            }
        }

        public void CleanupTemporaryFiles(string directoryPath)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return;

                // Clean up temporary files (older than 1 day)
                var tempFiles = Directory.GetFiles(directoryPath, "*.tmp")
                    .Concat(Directory.GetFiles(directoryPath, "*~"))
                    .Where(f => File.GetCreationTime(f) < DateTime.Now.AddDays(-1));

                foreach (var tempFile in tempFiles)
                {
                    try
                    {
                        File.Delete(tempFile);
                    }
                    catch
                    {
                        // Ignore individual file deletion errors
                    }
                }

                // Clean up empty subdirectories if created by this tool
                if (Directory.GetDirectories(directoryPath).Any())
                {
                    foreach (var subDir in Directory.GetDirectories(directoryPath))
                    {
                        try
                        {
                            if (!Directory.GetFiles(subDir).Any() && !Directory.GetDirectories(subDir).Any())
                            {
                                Directory.Delete(subDir);
                            }
                        }
                        catch
                        {
                            // Ignore directory deletion errors
                        }
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors - this is not critical
            }
        }

        private string SanitizeDirectoryName(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "Default";

            var invalidChars = Path.GetInvalidPathChars().Concat(Path.GetInvalidFileNameChars()).Distinct();
            var sanitized = input;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Replace multiple underscores with single underscore
            while (sanitized.Contains("__"))
            {
                sanitized = sanitized.Replace("__", "_");
            }

            // Trim and limit length
            sanitized = sanitized.Trim('_', ' ');
            if (sanitized.Length > 50)
            {
                sanitized = sanitized.Substring(0, 50).TrimEnd('_', ' ');
            }

            return string.IsNullOrEmpty(sanitized) ? "Default" : sanitized;
        }
    }
}
