using Autodesk.Revit.DB;
using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.Generic;

namespace GEN.ModelGroupExporter.Models
{
    /// <summary>
    /// Represents a Revit model group type with metadata for export operations
    /// </summary>
    public partial class ModelGroup : ObservableObject
    {
        /// <summary>
        /// The Revit GroupType element
        /// </summary>
        public GroupType RevitGroupType { get; set; }

        /// <summary>
        /// Unique identifier for the group
        /// </summary>
        public ElementId Id { get; set; }

        /// <summary>
        /// Display name of the group
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Group type name
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// Number of elements in the group
        /// </summary>
        public int ElementCount { get; set; }

        /// <summary>
        /// List of levels referenced by elements in this group
        /// </summary>
        public List<LevelModel> ReferencedLevels { get; set; }

        /// <summary>
        /// Bounding box of the group
        /// </summary>
        public BoundingBoxXYZ BoundingBox { get; set; }

        /// <summary>
        /// Whether this group is selected for export
        /// </summary>
        [ObservableProperty]
        private bool isSelected;

        /// <summary>
        /// Category breakdown of elements in the group
        /// </summary>
        public Dictionary<string, int> CategoryBreakdown { get; set; }

        public ModelGroup()
        {
            ReferencedLevels = new List<LevelModel>();
            CategoryBreakdown = new Dictionary<string, int>();
            isSelected = false;
        }

        public override string ToString()
        {
            return $"{Name} ({ElementCount} elements)";
        }
    }
}