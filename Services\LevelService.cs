using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using System.Collections.Generic;
using System.Linq;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of ILevelService for managing levels in Revit documents
    /// </summary>
    public class LevelService : ILevelService
    {
        public List<LevelModel> GetReferencedLevels(Document document, ICollection<ElementId> groupElements)
        {
            var referencedLevels = new HashSet<LevelModel>();

            foreach (var elementId in groupElements)
            {
                var element = document.GetElement(elementId);
                if (element == null) continue;

                // Check for level-based elements
                var levelParam = element.get_Parameter(BuiltInParameter.LEVEL_PARAM);
                if (levelParam != null && levelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(levelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // Check for reference level parameter
                var refLevelParam = element.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM);
                if (refLevelParam != null && refLevelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(refLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // Check for top level parameter (for elements like walls)
                var topLevelParam = element.get_Parameter(BuiltInParameter.WALL_HEIGHT_TYPE);
                if (topLevelParam != null && topLevelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(topLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // Check for base level parameter (stairs, ramps, etc.)
                var baseLevelParam = element.get_Parameter(BuiltInParameter.STAIRS_BASE_LEVEL_PARAM);
                if (baseLevelParam != null && baseLevelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(baseLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // Check for top level parameter (stairs, ramps, etc.)
                var stairsTopLevelParam = element.get_Parameter(BuiltInParameter.STAIRS_TOP_LEVEL_PARAM);
                if (stairsTopLevelParam != null && stairsTopLevelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(stairsTopLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // Check for RBS start level parameter (MEP systems)
                var rbsStartLevelParam = element.get_Parameter(BuiltInParameter.RBS_START_LEVEL_PARAM);
                if (rbsStartLevelParam != null && rbsStartLevelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(rbsStartLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // Check for schedule level parameter
                var scheduleLevelParam = element.get_Parameter(BuiltInParameter.SCHEDULE_LEVEL_PARAM);
                if (scheduleLevelParam != null && scheduleLevelParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var level = document.GetElement(scheduleLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                    if (level != null)
                    {
                        referencedLevels.Add(new LevelModel(level));
                    }
                }

                // For hosted elements, check the host's level
                if (element is FamilyInstance familyInstance && familyInstance.Host != null)
                {
                    var hostLevelParam = familyInstance.Host.get_Parameter(BuiltInParameter.LEVEL_PARAM);
                    if (hostLevelParam != null && hostLevelParam.AsElementId() != ElementId.InvalidElementId)
                    {
                        var level = document.GetElement(hostLevelParam.AsElementId()) as Autodesk.Revit.DB.Level;
                        if (level != null)
                        {
                            referencedLevels.Add(new LevelModel(level));
                        }
                    }
                }
            }

            var levelList = referencedLevels.ToList();
            System.Diagnostics.Debug.WriteLine($"Found {levelList.Count} referenced levels: {string.Join(", ", levelList.Select(l => l.Name))}");
            return levelList;
        }

        public Dictionary<ElementId, ElementId> CreateLevelsInDocument(Document targetDocument, List<LevelModel> sourceLevels)
        {
            var levelMapping = new Dictionary<ElementId, ElementId>();

            using (var trans = new Transaction(targetDocument, "Create levels"))
            {
                trans.Start();
                try
                {
                    foreach (var sourceLevel in sourceLevels)
                    {
                        // Check if level already exists
                        var existingLevel = FindMatchingLevel(targetDocument, sourceLevel.Name, sourceLevel.Elevation);
                        if (existingLevel != null)
                        {
                            levelMapping[sourceLevel.Id] = existingLevel.Id;
                            System.Diagnostics.Debug.WriteLine($"Using existing level: {sourceLevel.Name} -> {existingLevel.Id}");
                            continue;
                        }

                        // Create new level
                        try
                        {
                            var newLevel = Autodesk.Revit.DB.Level.Create(targetDocument, sourceLevel.Elevation);
                            newLevel.Name = sourceLevel.Name;

                            // Set level properties
                            var structuralParam = newLevel.get_Parameter(BuiltInParameter.LEVEL_IS_STRUCTURAL);
                            if (structuralParam != null)
                            {
                                structuralParam.Set(sourceLevel.IsStructural ? 1 : 0);
                            }

                            var buildingStoryParam = newLevel.get_Parameter(BuiltInParameter.LEVEL_IS_BUILDING_STORY);
                            if (buildingStoryParam != null)
                            {
                                buildingStoryParam.Set(sourceLevel.IsBuildingStory ? 1 : 0);
                            }

                            levelMapping[sourceLevel.Id] = newLevel.Id;
                            System.Diagnostics.Debug.WriteLine($"Created new level: {sourceLevel.Name} (ID: {sourceLevel.Id}) -> {newLevel.Id}");
                        }
                        catch (System.Exception ex)
                        {
                            // Log error but continue with other levels
                            System.Diagnostics.Debug.WriteLine($"Failed to create level {sourceLevel.Name}: {ex.Message}");
                        }
                    }

                    trans.Commit();
                    System.Diagnostics.Debug.WriteLine($"Level mapping created with {levelMapping.Count} entries");
                }
                catch (Exception ex)
                {
                    trans.RollBack();
                    System.Diagnostics.Debug.WriteLine($"Failed to create levels: {ex.Message}");
                    throw;
                }
            }

            return levelMapping;
        }

        public List<LevelModel> GetAllLevels(Document document)
        {
            var levels = new List<LevelModel>();

            var collector = new FilteredElementCollector(document)
                .OfClass(typeof(Autodesk.Revit.DB.Level))
                .Cast<Autodesk.Revit.DB.Level>();

            foreach (var revitLevel in collector)
            {
                levels.Add(new LevelModel(revitLevel));
            }

            return levels.OrderBy(l => l.Elevation).ToList();
        }

        public Autodesk.Revit.DB.Level FindMatchingLevel(Document document, string levelName, double elevation, double tolerance = 0.001)
        {
            var collector = new FilteredElementCollector(document)
                .OfClass(typeof(Autodesk.Revit.DB.Level))
                .Cast<Autodesk.Revit.DB.Level>();

            return collector.FirstOrDefault(level =>
                level.Name == levelName &&
                System.Math.Abs(level.Elevation - elevation) < tolerance);
        }

        public bool ValidateLevelsExist(Document targetDocument, List<LevelModel> requiredLevels)
        {
            foreach (var requiredLevel in requiredLevels)
            {
                var existingLevel = FindMatchingLevel(targetDocument, requiredLevel.Name, requiredLevel.Elevation);
                if (existingLevel == null)
                {
                    return false;
                }
            }

            return true;
        }
    }
}
