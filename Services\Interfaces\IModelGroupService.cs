using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using System.Collections.Generic;

namespace GEN.ModelGroupExporter.Services.Interfaces
{
    /// <summary>
    /// Service for discovering and analyzing model groups in Revit documents
    /// </summary>
    public interface IModelGroupService
    {
        /// <summary>
        /// Gets all model groups from the specified document
        /// </summary>
        /// <param name="document">The Revit document to analyze</param>
        /// <returns>List of model groups with metadata</returns>
        List<ModelGroup> GetModelGroups(Document document);

        /// <summary>
        /// Gets detailed information about a specific model group type
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupTypeId">The group type element ID</param>
        /// <returns>Detailed model group information</returns>
        ModelGroup GetModelGroupDetails(Document document, ElementId groupTypeId);

        /// <summary>
        /// Gets all elements within a model group type
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupType">The model group type</param>
        /// <returns>Collection of elements in the group type</returns>
        ICollection<ElementId> GetGroupElements(Document document, GroupType groupType);

        /// <summary>
        /// Gets group elements along with the group origin information for proper transformation
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupType">The GroupType to get elements from</param>
        /// <returns>Tuple containing element IDs and the group origin transformation</returns>
        (ICollection<ElementId> ElementIds, Transform GroupOriginTransform) GetGroupElementsWithOrigin(Document document, GroupType groupType);

        /// <summary>
        /// Analyzes the category breakdown of elements in a group
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="group">The model group</param>
        /// <returns>Dictionary of category names and element counts</returns>
        Dictionary<string, int> AnalyzeCategoryBreakdown(Document document, GroupType groupType);

        /// <summary>
        /// Gets the bounding box for a model group
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="group">The model group</param>
        /// <returns>Bounding box of the group</returns>
        BoundingBoxXYZ GetGroupBoundingBox(Document document, Group group);
    }
}
