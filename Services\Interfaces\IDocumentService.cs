using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;

namespace GEN.ModelGroupExporter.Services.Interfaces
{
    /// <summary>
    /// Service for creating and managing Revit documents
    /// </summary>
    public interface IDocumentService
    {
        /// <summary>
        /// Creates a new Revit document for exporting a model group
        /// </summary>
        /// <param name="application">The Revit application</param>
        /// <param name="configuration">Export configuration settings</param>
        /// <returns>New Revit document</returns>
        Document CreateNewDocument(Autodesk.Revit.ApplicationServices.Application application, ExportConfiguration configuration);

        /// <summary>
        /// Saves a document to the specified path
        /// </summary>
        /// <param name="document">The document to save</param>
        /// <param name="filePath">Full path where to save the document</param>
        /// <param name="configuration">Export configuration settings</param>
        void SaveDocument(Document document, string filePath, ExportConfiguration configuration);

        /// <summary>
        /// Purges unused elements from a document to reduce file size
        /// </summary>
        /// <param name="document">The document to purge</param>
        /// <param name="transaction">The active transaction</param>
        void PurgeUnusedElements(Document document);

        /// <summary>
        /// Gets the file size of a document in MB
        /// </summary>
        /// <param name="filePath">Path to the document file</param>
        /// <returns>File size in MB</returns>
        double GetDocumentSizeMB(string filePath);

        /// <summary>
        /// Validates that a document was created successfully
        /// </summary>
        /// <param name="document">The document to validate</param>
        /// <returns>True if document is valid</returns>
        bool ValidateDocument(Document document);

        /// <summary>
        /// Closes a document safely
        /// </summary>
        /// <param name="document">The document to close</param>
        /// <param name="saveChanges">Whether to save changes before closing</param>
        void CloseDocument(Document document, bool saveChanges = false);
    }
}
