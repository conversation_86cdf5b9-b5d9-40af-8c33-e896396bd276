using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using System;
using System.Collections.Generic;

namespace GEN.ModelGroupExporter.Services.Interfaces
{
    /// <summary>
    /// Result of an export operation
    /// </summary>
    public class ExportResult
    {
        public bool Success { get; set; }
        public string FilePath { get; set; }
        public double FileSizeMB { get; set; }
        public int ElementCount { get; set; }
        public string ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        
        public ExportResult()
        {
            Success = false;
            FilePath = string.Empty;
            ErrorMessage = string.Empty;
        }
    }

    ///// <summary>
    ///// Progress information for export operations
    ///// </summary>
    //public class ExportProgress
    //{
    //    public int CurrentGroup { get; set; }
    //    public int TotalGroups { get; set; }
    //    public string CurrentGroupName { get; set; }
    //    public string CurrentOperation { get; set; }
    //    public double PercentComplete => TotalGroups > 0 ? (double)CurrentGroup / TotalGroups * 100 : 0;
    //}

    /// <summary>
    /// Main service for orchestrating the export process
    /// </summary>
    public interface IExportService
    {
        ///// <summary>
        ///// Event raised when export progress changes
        ///// </summary>
        //event EventHandler<ExportProgress> ProgressChanged;

        /// <summary>
        /// Exports selected model groups to individual files
        /// </summary>
        /// <param name="sourceDocument">The source Revit document</param>
        /// <param name="application">The Revit application</param>
        /// <param name="selectedGroups">List of groups to export</param>
        /// <param name="configuration">Export configuration settings</param>
        /// <returns>List of export results for each group</returns>
        List<ExportResult> ExportModelGroups(Document sourceDocument, 
            Autodesk.Revit.ApplicationServices.Application application,
            List<ModelGroup> selectedGroups, ExportConfiguration configuration);

        /// <summary>
        /// Exports a single model group to a file
        /// </summary>
        /// <param name="sourceDocument">The source Revit document</param>
        /// <param name="application">The Revit application</param>
        /// <param name="modelGroup">The group to export</param>
        /// <param name="configuration">Export configuration settings</param>
        /// <param name="fileIndex">Index for handling duplicate names</param>
        /// <returns>Export result</returns>
        ExportResult ExportSingleGroup(Document sourceDocument, 
            Autodesk.Revit.ApplicationServices.Application application,
            ModelGroup modelGroup, ExportConfiguration configuration, int fileIndex = 0);

        /// <summary>
        /// Exports multiple model groups to a single combined Revit document
        /// </summary>
        /// <param name="sourceDocument">The source Revit document</param>
        /// <param name="application">The Revit application</param>
        /// <param name="selectedGroups">List of groups to export</param>
        /// <param name="configuration">Export configuration settings</param>
        /// <returns>Export result for the combined document</returns>
        ExportResult ExportMultipleGroupsToSingleDocument(Document sourceDocument,
            Autodesk.Revit.ApplicationServices.Application application,
            List<ModelGroup> selectedGroups, ExportConfiguration configuration);

        /// <summary>
        /// Validates export configuration before starting export
        /// </summary>
        /// <param name="configuration">Configuration to validate</param>
        /// <returns>True if configuration is valid</returns>
        bool ValidateConfiguration(ExportConfiguration configuration);

    }
}
