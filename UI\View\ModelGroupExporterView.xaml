<Window
    x:Class="GEN.ModelGroupExporter.UI.View.ModelGroupExporterView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    Title="Beca Tools | General"
    Width="660"
    Height="700"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>

    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <TextBlock
            Grid.Row="0"
            Grid.ColumnSpan="5"
            Margin="15,5,0,10"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="MODEL GROUP EXPORTER" />
        <Button
            Grid.Column="6"
            Height="45"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"
            Background="Transparent"
            BorderBrush="Transparent"
            Command="{Binding OpenDocumentationCommand}"
            Content="{materialDesign:PackIcon Kind=HelpCircleOutline,
                                              Size=38}"
            Foreground="#12A8B2" />
        <Separator
            Grid.ColumnSpan="6"
            Margin="10,45,15,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Main Content  -->
        <Grid Grid.Row="1" Margin="10,0,10,0">
            <!--  Left Panel - Group Selection  -->
            <Border>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  Group Selection Header  -->
                    <StackPanel
                        Grid.Row="0"
                        Margin="10,10,0,10"
                        Orientation="Horizontal">
                        <TextBlock FontWeight="Bold" Text="Model Groups" />
                        <TextBlock
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            Text="{Binding SelectedGroupCount, StringFormat='({0} selected)'}" />
                    </StackPanel>

                    <!--  Selection Buttons  -->
                    <StackPanel
                        Grid.Row="1"
                        Margin="0,0,0,10"
                        Orientation="Horizontal">
                        <TextBlock
                            Margin="10,0,0,0" FontWeight="DemiBold" FontSize="12"
                            VerticalAlignment="Center"
                            Text="Select:" />
                        <Button
                            Width="60"
                            Height="25"
                            Margin="10,0,10,0"
                            Padding="0"
                            Background="#12A8B2"
                            BorderBrush="#12A8B2"
                            Command="{Binding SelectAllCommand}"
                            Content="All"
                            FontWeight="Regular"
                            Foreground="White" />
                        <Button
                            Width="60"
                            Height="25"
                            Margin="0,0,5,0"
                            Padding="0"
                            Background="#12A8B2"
                            BorderBrush="#12A8B2"
                            Command="{Binding SelectNoneCommand}"
                            Content="None"
                            FontWeight="Regular"
                            Foreground="White" />
                    </StackPanel>

                    <!--  Groups List  -->
                    <DataGrid
                        Grid.Row="2"
                        Margin="5,0,5,10"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        CanUserReorderColumns="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        ItemsSource="{Binding ModelGroups}"
                        SelectionMode="Extended">
                        <DataGrid.Columns>
                            <DataGridCheckBoxColumn
                                Width="80"
                                Binding="{Binding IsSelected}"
                                Header="Export" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Name}"
                                Header="Name"
                                IsReadOnly="True" />
                            <!--<DataGridTextColumn
                                Width="120"
                                Binding="{Binding TypeName}"
                                Header="Type"
                                IsReadOnly="True" />-->
                            <DataGridTextColumn
                                Width="90"
                                Binding="{Binding ElementCount}"
                                Header="Elements"
                                IsReadOnly="True" />
                            <DataGridTextColumn
                                Width="70"
                                Binding="{Binding ReferencedLevels.Count}"
                                Header="Levels"
                                IsReadOnly="True" />
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

        </Grid>

        <!--  Progress Bar  -->
        <Border Grid.Row="2" Margin="10,0,10,10">
            <StackPanel>
                <TextBlock FontWeight="Bold" Text="Export File" />

                <!--  Export Directory  -->
                <TextBlock Margin="0,10,0,2" Text="Export Directory:" />
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <TextBox
                        Grid.Column="0"
                        Height="25"
                        IsReadOnly="True"
                        Text="{Binding Configuration.ExportDirectory}" />
                    <Button
                        Grid.Column="1"
                        Width="90"
                        Height="30"
                        Margin="15,0,10,0"
                        Background="#12A8B2"
                        BorderBrush="#12A8B2"
                        Command="{Binding BrowseExportDirectoryCommand}"
                        Content="Browse..."
                        FontWeight="Regular"
                        Foreground="White" />
                </Grid>

                <!--  File Naming  -->
                <!--
                <TextBlock Margin="0,10,0,2" Text="File Name Pattern:" />
                <TextBox Height="25" Text="{Binding Configuration.FileNamePattern}" />
                <TextBlock
                    Margin="0,2,0,0"
                    FontSize="10"
                    Foreground="Gray"
                    Text="Available tokens: {Date}, {Time}"
                    TextWrapping="Wrap" />-->
            </StackPanel>
        </Border>

        <!--  Status Bar  -->
        <Border
            Grid.Row="3"
            Margin="10,15,10,0"
            Padding="10,5"
            Background="WhiteSmoke"
            BorderBrush="LightGray"
            BorderThickness="0,1,0,0">
            <TextBlock FontSize="11" Text="{Binding StatusMessage}" />
        </Border>

        <!--  Action Buttons  -->
        <StackPanel
            Grid.Row="4"
            Margin="0,10,10,0"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            <Button
                Width="180"
                Height="30"
                Margin="0,0,20,0"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Command="{Binding ExportSelectedGroupsCommand}"
                Content="Export Selected Groups"
                Foreground="White" />
            <Button
                Width="80"
                Height="30"
                Background="#FFCE00"
                BorderBrush="#FFCE00"
                Content="Close"
                FontWeight="Bold"
                IsCancel="True" />
        </StackPanel>

        <!--  Footer  -->
        <Grid Grid.Row="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Border
                Width="120"
                Margin="10"
                HorizontalAlignment="Left">
                <Image
                    RenderOptions.BitmapScalingMode="HighQuality"
                    RenderOptions.EdgeMode="Aliased"
                    SnapsToDevicePixels="True"
                    Source="/GEN.ModelGroupExporter;component/UI/View/BecaLogoBlack.png"
                    Stretch="Uniform"
                    StretchDirection="Both"
                    UseLayoutRounding="True" />
            </Border>
            <TextBlock
                Grid.Column="1"
                Margin="0,10,10,0"
                VerticalAlignment="Center"
                FontFamily="Arial"
                FontSize="20"
                FontWeight="Bold"
                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                Text="Make Everyday Better" />
        </Grid>
    </Grid>
</Window>
