using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using System.Collections.Generic;
using System.Linq;

namespace GEN.ModelGroupExporter.Converter
{
    /// <summary>
    /// Utility class for converting between Revit objects and domain models
    /// </summary>
    public static class ModelGroupConverter
    {
        /// <summary>
        /// Creates a temporary group instance to access member IDs, then returns it for cleanup
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupType">The GroupType to create an instance of</param>
        /// <returns>A temporary Group instance, or null if creation failed</returns>
        private static Group CreateTemporaryGroupInstance(Document document, GroupType groupType)
        {
            Group temporaryInstance = null;

            try
            {
                using (var trans = new Transaction(document, "Create temporary group instance"))
                {
                    trans.Start();

                    try
                    {
                        // Create a temporary instance at origin
                        var location = XYZ.Zero;
                        temporaryInstance = document.Create.PlaceGroup(location, groupType);

                        if (temporaryInstance != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"Converter: Created temporary instance (ID: {temporaryInstance.Id}) for GroupType '{groupType.Name}'");
                            trans.Commit();
                            return temporaryInstance;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"Converter: Failed to create temporary instance for GroupType '{groupType.Name}'");
                            trans.RollBack();
                            return null;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Converter: Error creating temporary instance for GroupType '{groupType.Name}': {ex.Message}");
                        trans.RollBack();
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Converter: Transaction error creating temporary instance for GroupType '{groupType.Name}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Deletes a temporary group instance that was created for accessing member IDs
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="temporaryInstance">The temporary Group instance to delete</param>
        private static void DeleteTemporaryGroupInstance(Document document, Group temporaryInstance)
        {
            try
            {
                using (var trans = new Transaction(document, "Delete temporary group instance"))
                {
                    trans.Start();

                    try
                    {
                        document.Delete(temporaryInstance.Id);
                        System.Diagnostics.Debug.WriteLine($"Converter: Deleted temporary group instance (ID: {temporaryInstance.Id})");
                        trans.Commit();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Converter: Error deleting temporary group instance (ID: {temporaryInstance.Id}): {ex.Message}");
                        trans.RollBack();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Converter: Transaction error deleting temporary group instance (ID: {temporaryInstance.Id}): {ex.Message}");
            }
        }
        /// <summary>
        /// Converts a Revit Level to a LevelModel domain object
        /// </summary>
        /// <param name="revitLevel">The Revit Level</param>
        /// <returns>LevelModel domain object</returns>
        public static LevelModel ToLevel(Autodesk.Revit.DB.Level revitLevel)
        {
            if (revitLevel == null)
                return null;

            return new LevelModel(revitLevel);
        }

        /// <summary>
        /// Converts multiple Revit Levels to LevelModel domain objects
        /// </summary>
        /// <param name="revitLevels">Collection of Revit Levels</param>
        /// <returns>List of LevelModel domain objects</returns>
        public static List<LevelModel> ToLevels(IEnumerable<Autodesk.Revit.DB.Level> revitLevels)
        {
            if (revitLevels == null)
                return new List<LevelModel>();

            return revitLevels.Select(ToLevel).Where(l => l != null).ToList();
        }

        /// <summary>
        /// Gets element category information for reporting
        /// </summary>
        /// <param name="element">The Revit element</param>
        /// <returns>Category information</returns>
        public static string GetElementCategoryInfo(Element element)
        {
            if (element?.Category != null)
            {
                return element.Category.Name;
            }

            return element?.GetType().Name ?? "Unknown";
        }

        /// <summary>
        /// Gets element type information for reporting
        /// </summary>
        /// <param name="element">The Revit element</param>
        /// <param name="document">The document containing the element</param>
        /// <returns>Type information</returns>
        public static string GetElementTypeInfo(Element element, Document document)
        {
            if (element == null)
                return "Unknown";

            var typeId = element.GetTypeId();
            if (typeId != ElementId.InvalidElementId)
            {
                var elementType = document.GetElement(typeId) as ElementType;
                if (elementType != null)
                {
                    return $"{elementType.FamilyName} : {elementType.Name}";
                }
            }

            return element.GetType().Name;
        }

        /// <summary>
        /// Extracts parameter values from an element for reporting
        /// </summary>
        /// <param name="element">The Revit element</param>
        /// <param name="parameterNames">Names of parameters to extract</param>
        /// <returns>Dictionary of parameter values</returns>
        public static Dictionary<string, string> ExtractParameterValues(Element element, params string[] parameterNames)
        {
            var parameterValues = new Dictionary<string, string>();

            if (element == null || parameterNames == null)
                return parameterValues;

            foreach (var paramName in parameterNames)
            {
                try
                {
                    var parameter = element.LookupParameter(paramName);
                    if (parameter != null && parameter.HasValue)
                    {
                        parameterValues[paramName] = GetParameterValueAsString(parameter);
                    }
                }
                catch
                {
                    // Ignore parameter extraction errors
                }
            }

            return parameterValues;
        }

        /// <summary>
        /// Gets a parameter value as a string
        /// </summary>
        /// <param name="parameter">The parameter</param>
        /// <returns>String representation of the parameter value</returns>
        public static string GetParameterValueAsString(Parameter parameter)
        {
            if (parameter == null || !parameter.HasValue)
                return string.Empty;

            switch (parameter.StorageType)
            {
                case StorageType.Double:
                    return parameter.AsDouble().ToString("F3");
                case StorageType.Integer:
                    return parameter.AsInteger().ToString();
                case StorageType.String:
                    return parameter.AsString() ?? string.Empty;
                case StorageType.ElementId:
                    var elementId = parameter.AsElementId();
                    return elementId != ElementId.InvalidElementId ? elementId.ToString() : string.Empty;
                default:
                    return parameter.AsValueString() ?? string.Empty;
            }
        }

        private static Dictionary<string, int> AnalyzeCategoryBreakdown(Document document, ICollection<ElementId> memberIds)
        {
            var categoryBreakdown = new Dictionary<string, int>();

            foreach (var memberId in memberIds)
            {
                var element = document.GetElement(memberId);
                if (element?.Category != null)
                {
                    var categoryName = element.Category.Name;
                    if (categoryBreakdown.ContainsKey(categoryName))
                    {
                        categoryBreakdown[categoryName]++;
                    }
                    else
                    {
                        categoryBreakdown[categoryName] = 1;
                    }
                }
            }

            return categoryBreakdown;
        }

        private static BoundingBoxXYZ GetGroupBoundingBox(Group group, Document document, ICollection<ElementId> memberIds)
        {
            try
            {
                // Try to get the group's bounding box directly
                var groupBoundingBox = group.get_BoundingBox(null);
                if (groupBoundingBox != null)
                {
                    return groupBoundingBox;
                }

                // Calculate from member elements
                if (!memberIds.Any()) return null;

                XYZ min = null;
                XYZ max = null;

                foreach (var memberId in memberIds)
                {
                    var element = document.GetElement(memberId);
                    var elementBoundingBox = element?.get_BoundingBox(null);

                    if (elementBoundingBox != null)
                    {
                        if (min == null)
                        {
                            min = elementBoundingBox.Min;
                            max = elementBoundingBox.Max;
                        }
                        else
                        {
                            min = new XYZ(
                                System.Math.Min(min.X, elementBoundingBox.Min.X),
                                System.Math.Min(min.Y, elementBoundingBox.Min.Y),
                                System.Math.Min(min.Z, elementBoundingBox.Min.Z)
                            );
                            max = new XYZ(
                                System.Math.Max(max.X, elementBoundingBox.Max.X),
                                System.Math.Max(max.Y, elementBoundingBox.Max.Y),
                                System.Math.Max(max.Z, elementBoundingBox.Max.Z)
                            );
                        }
                    }
                }

                if (min != null && max != null)
                {
                    return new BoundingBoxXYZ
                    {
                        Min = min,
                        Max = max
                    };
                }
            }
            catch
            {
                // Return null if bounding box calculation fails
            }

            return null;
        }
    }
}
