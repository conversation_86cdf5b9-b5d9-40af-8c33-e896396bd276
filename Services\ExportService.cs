using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using GEN.ModelGroupExporter.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Transactions;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of IExportService for orchestrating the export process
    /// </summary>
    public class ExportService : IExportService
    {
        private readonly IModelGroupService _modelGroupService;
        private readonly ILevelService _levelService;
        private readonly IDocumentService _documentService;
        private readonly IElementTransferService _elementTransferService;

        public ExportService(IModelGroupService modelGroupService, ILevelService levelService,
            IDocumentService documentService, IElementTransferService elementTransferService)
        {
            _modelGroupService = modelGroupService;
            _levelService = levelService;
            _documentService = documentService;
            _elementTransferService = elementTransferService;
        }

        public List<ExportResult> ExportModelGroups(Document sourceDocument,
            Autodesk.Revit.ApplicationServices.Application application,
            List<ModelGroup> selectedGroups, ExportConfiguration configuration)
        {
            var results = new List<ExportResult>();

            try
            {
                // Validate configuration
                if (!ValidateConfiguration(configuration))
                {
                    throw new InvalidOperationException("Invalid export configuration");
                }

                var totalGroups = selectedGroups.Count;
                var currentGroup = 0;

                foreach (var modelGroup in selectedGroups)
                {
                    currentGroup++;

                    // Export single group
                    var result = ExportSingleGroup(sourceDocument, application, modelGroup, configuration);
                    results.Add(result);

                    // Check for duplicate names and retry if needed
                    if (!result.Success && result.ErrorMessage.Contains("file already exists"))
                    {
                        var retryIndex = 1;
                        while (retryIndex <= 10 && !result.Success)
                        {
                            result = ExportSingleGroup(sourceDocument, application, modelGroup, configuration, retryIndex);
                            retryIndex++;
                        }
                        results[results.Count - 1] = result; // Update the result
                    }
                }
            }
            catch (Exception ex)
            {
                // Add error result for any unhandled exceptions
                results.Add(new ExportResult
                {
                    Success = false,
                    ErrorMessage = $"Export failed: {ex.Message}"
                });
            }
            finally
            {

            }

            return results;
        }

        /// <summary>
        /// Exports multiple model groups to a single Revit document
        /// </summary>
        /// <param name="sourceDocument">The source Revit document</param>
        /// <param name="application">The Revit application</param>
        /// <param name="selectedGroups">List of groups to export</param>
        /// <param name="configuration">Export configuration settings</param>
        /// <returns>Export result for the combined document</returns>
        public ExportResult ExportMultipleGroupsToSingleDocument(Document sourceDocument,
            Autodesk.Revit.ApplicationServices.Application application,
            List<ModelGroup> selectedGroups, ExportConfiguration configuration)
        {
            var result = new ExportResult();
            Document targetDocument = null;

            try
            {
                if (selectedGroups == null || selectedGroups.Count == 0)
                {
                    result.Success = false;
                    result.ErrorMessage = "No groups selected for export";
                    return result;
                }

                // Generate file path for the combined export
                var fileName = configuration.GenerateFileNameForCombinedModelGroup();
                var filePath = Path.Combine(configuration.ExportDirectory, fileName + ".rvt");

                // Check if file already exists
                if (File.Exists(filePath))
                {
                    // Try with timestamp to make it unique
                    fileName = $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}";
                    filePath = Path.Combine(configuration.ExportDirectory, fileName + ".rvt");
                }

                System.Diagnostics.Debug.WriteLine($"Creating combined document for {selectedGroups.Count} groups: {fileName}");

                // Create new document
                targetDocument = _documentService.CreateNewDocument(application, configuration);
                if (!_documentService.ValidateDocument(targetDocument))
                {
                    result.Success = false;
                    result.ErrorMessage = "Failed to create target document";
                    return result;
                }

                // Collect all referenced levels from all selected groups first
                var allReferencedLevels = new HashSet<LevelModel>();
                var totalElementCount = 0;

                foreach (var modelGroup in selectedGroups)
                {
                    System.Diagnostics.Debug.WriteLine($"Processing group: {modelGroup.Name}");

                    // Get group elements with origin information
                    var (groupElements, groupOriginTransform) = _modelGroupService.GetGroupElementsWithOrigin(sourceDocument, modelGroup.RevitGroupType);
                    totalElementCount += groupElements.Count;

                    // Get referenced levels
                    var referencedLevels = _levelService.GetReferencedLevels(sourceDocument, groupElements);
                    foreach (var level in referencedLevels)
                    {
                        allReferencedLevels.Add(level);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Combined export: {totalElementCount} elements from {selectedGroups.Count} groups, {allReferencedLevels.Count} unique levels");

                // Create all required levels in target document
                var levelMapping = _levelService.CreateLevelsInDocument(targetDocument, allReferencedLevels.ToList());

                // Copy each group separately to preserve their individual origin relationships
                var allCopiedElements = new List<ElementId>();
                foreach (var modelGroup in selectedGroups)
                {
                    var (groupElements, groupOriginTransform) = _modelGroupService.GetGroupElementsWithOrigin(sourceDocument, modelGroup.RevitGroupType);

                    // Copy elements for this group with its specific origin transformation
                    var copiedElements = _elementTransferService.CopyElementsWithTransform(sourceDocument, targetDocument,
                        groupElements, levelMapping, groupOriginTransform);
                    allCopiedElements.AddRange(copiedElements);

                    System.Diagnostics.Debug.WriteLine($"Copied {copiedElements.Count} elements from group '{modelGroup.Name}' with origin transform");
                }

                System.Diagnostics.Debug.WriteLine($"Total copied {allCopiedElements.Count} elements from {selectedGroups.Count} groups to target document");


                // Validate level references before updating
                ValidateLevelReferences(targetDocument, allCopiedElements, "Before UpdateElementHosting");

                // Update element level references after copying
                _elementTransferService.UpdateElementHosting(targetDocument, allCopiedElements, levelMapping);

                // Validate level references after updating
                ValidateLevelReferences(targetDocument, allCopiedElements, "After UpdateElementHosting");

                // Purge unused elements if configured
                if (configuration.PurgeUnusedElements)
                {
                    _documentService.PurgeUnusedElements(targetDocument);
                }

                // Save document
                _documentService.SaveDocument(targetDocument, filePath, configuration);

                // Get file size
                var fileSizeMB = _documentService.GetDocumentSizeMB(filePath);

                result.Success = true;
                result.FilePath = filePath;
                result.FileSizeMB = fileSizeMB;
                result.ElementCount = allCopiedElements.Count;

                System.Diagnostics.Debug.WriteLine($"Combined export completed successfully: {result.ElementCount} elements, {result.FileSizeMB:F2} MB");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                System.Diagnostics.Debug.WriteLine($"Combined export failed: {ex.Message}");
            }
            finally
            {
                // Close target document
                if (targetDocument != null)
                {
                    _documentService.CloseDocument(targetDocument, false);
                }
            }

            return result;
        }

        public ExportResult ExportSingleGroup(Document sourceDocument,
            Autodesk.Revit.ApplicationServices.Application application,
            ModelGroup modelGroup, ExportConfiguration configuration, int fileIndex = 0)
        {
            var result = new ExportResult();
            //var startTime = DateTime.Now;
            Document targetDocument = null;

            try
            {
                // Generate file path
                var fileName = modelGroup.Name; // configuration.GenerateFileNameForSingleModelGroup(modelGroup, fileIndex);
                var filePath = Path.Combine(configuration.ExportDirectory, fileName + ".rvt");

                // Check if file already exists
                if (File.Exists(filePath))
                {
                    result.Success = false;
                    result.ErrorMessage = "File already exists";
                    return result;
                }

                // Create new document
                targetDocument = _documentService.CreateNewDocument(application, configuration);
                if (!_documentService.ValidateDocument(targetDocument))
                {
                    result.Success = false;
                    result.ErrorMessage = "Failed to create target document";
                    return result;
                }

                try
                {
                    // Get group elements with origin information to preserve group coordinate system
                    var (groupElements, groupOriginTransform) = _modelGroupService.GetGroupElementsWithOrigin(sourceDocument, modelGroup.RevitGroupType);

                    // Get referenced levels and create them in target document
                    var referencedLevels = _levelService.GetReferencedLevels(sourceDocument, groupElements);
                    System.Diagnostics.Debug.WriteLine($"Exporting group '{modelGroup.Name}' with {groupElements.Count} elements and {referencedLevels.Count} referenced levels");

                    var levelMapping = _levelService.CreateLevelsInDocument(targetDocument, referencedLevels);
                    System.Diagnostics.Debug.WriteLine($"Level mapping created with {levelMapping.Count} entries");

                    // Copy elements to target document with group origin transformation to preserve relative positioning
                    var copiedElements = _elementTransferService.CopyElementsWithTransform(sourceDocument, targetDocument,
                        groupElements, levelMapping, groupOriginTransform);
                    System.Diagnostics.Debug.WriteLine($"Copied {copiedElements.Count} elements to target document with group origin transform");

                    // Validate level references before updating
                    ValidateLevelReferences(targetDocument, copiedElements, "Before UpdateElementHosting");

                    // Update element level references after copying
                    _elementTransferService.UpdateElementHosting(targetDocument, copiedElements, levelMapping);

                    // Validate level references after updating
                    ValidateLevelReferences(targetDocument, copiedElements, "After UpdateElementHosting");

                    // Purge unused elements if configured
                    _documentService.PurgeUnusedElements(targetDocument);

                    // Save document
                    _documentService.SaveDocument(targetDocument, filePath, configuration);

                    // Get file size
                    var fileSizeMB = _documentService.GetDocumentSizeMB(filePath);

                    result.Success = true;
                    result.FilePath = filePath;
                    result.FileSizeMB = fileSizeMB;
                    result.ElementCount = copiedElements.Count;
                }
                catch (Exception ex)
                {
                    result.Success = false;
                    result.ErrorMessage = ex.Message;
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                // Close target document
                if (targetDocument != null)
                {
                    _documentService.CloseDocument(targetDocument, false);
                }

            }

            return result;
        }

        public bool ValidateConfiguration(ExportConfiguration configuration)
        {
            if (configuration == null)
                return false;

            // Check export directory
            if (string.IsNullOrEmpty(configuration.ExportDirectory))
                return false;

            try
            {
                // Try to create directory if it doesn't exist
                if (!Directory.Exists(configuration.ExportDirectory))
                {
                    Directory.CreateDirectory(configuration.ExportDirectory);
                }
            }
            catch
            {
                return false;
            }

            // Check custom template path if specified
            if (!configuration.UseMinimalTemplate && !string.IsNullOrEmpty(configuration.CustomTemplatePath))
            {
                if (!File.Exists(configuration.CustomTemplatePath))
                {
                    return false;
                }
            }

            //// Check file name pattern
            //if (string.IsNullOrEmpty(configuration.FileNamePattern))
            //    return false;

            return true;
        }

        /// <summary>
        /// Validates level references in copied elements for debugging purposes
        /// </summary>
        /// <param name="targetDocument">Target document</param>
        /// <param name="copiedElements">Collection of copied element IDs</param>
        /// <param name="stage">Description of when this validation is being performed</param>
        private void ValidateLevelReferences(Document targetDocument, ICollection<ElementId> copiedElements, string stage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== Level Reference Validation - {stage} ===");

                int validReferences = 0;
                int invalidReferences = 0;
                int elementsWithoutLevels = 0;

                foreach (var elementId in copiedElements)
                {
                    var element = targetDocument.GetElement(elementId);
                    if (element == null) continue;

                    bool hasLevelReference = false;

                    // Check main level parameter
                    var levelParam = element.get_Parameter(BuiltInParameter.LEVEL_PARAM);
                    if (levelParam != null)
                    {
                        var levelId = levelParam.AsElementId();
                        if (levelId != ElementId.InvalidElementId)
                        {
                            hasLevelReference = true;
                            var level = targetDocument.GetElement(levelId) as Level;
                            if (level != null)
                            {
                                validReferences++;
                            }
                            else
                            {
                                invalidReferences++;
                                System.Diagnostics.Debug.WriteLine($"  Invalid level reference: Element {elementId} ({element.GetType().Name}) references non-existent level {levelId}");
                            }
                        }
                    }

                    // Check reference level parameter
                    var refLevelParam = element.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM);
                    if (refLevelParam != null)
                    {
                        var refLevelId = refLevelParam.AsElementId();
                        if (refLevelId != ElementId.InvalidElementId)
                        {
                            hasLevelReference = true;
                            var refLevel = targetDocument.GetElement(refLevelId) as Level;
                            if (refLevel == null)
                            {
                                invalidReferences++;
                                System.Diagnostics.Debug.WriteLine($"  Invalid reference level: Element {elementId} ({element.GetType().Name}) references non-existent reference level {refLevelId}");
                            }
                        }
                    }

                    // Check RBS start level parameter
                    var rbsStartLevelParam = element.get_Parameter(BuiltInParameter.RBS_START_LEVEL_PARAM);
                    if (rbsStartLevelParam != null)
                    {
                        var rbsStartLevelId = rbsStartLevelParam.AsElementId();
                        if (rbsStartLevelId != ElementId.InvalidElementId)
                        {
                            hasLevelReference = true;
                            var rbsStartLevel = targetDocument.GetElement(rbsStartLevelId) as Level;
                            if (rbsStartLevel == null)
                            {
                                invalidReferences++;
                                System.Diagnostics.Debug.WriteLine($"  Invalid RBS start level: Element {elementId} ({element.GetType().Name}) references non-existent RBS start level {rbsStartLevelId}");
                            }
                        }
                    }

                    // Check schedule level parameter
                    var scheduleLevelParam = element.get_Parameter(BuiltInParameter.SCHEDULE_LEVEL_PARAM);
                    if (scheduleLevelParam != null)
                    {
                        var scheduleLevelId = scheduleLevelParam.AsElementId();
                        if (scheduleLevelId != ElementId.InvalidElementId)
                        {
                            hasLevelReference = true;
                            var scheduleLevel = targetDocument.GetElement(scheduleLevelId) as Level;
                            if (scheduleLevel == null)
                            {
                                invalidReferences++;
                                System.Diagnostics.Debug.WriteLine($"  Invalid schedule level: Element {elementId} ({element.GetType().Name}) references non-existent schedule level {scheduleLevelId}");
                            }
                        }
                    }

                    if (!hasLevelReference)
                    {
                        elementsWithoutLevels++;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Level validation results: {validReferences} valid, {invalidReferences} invalid, {elementsWithoutLevels} without levels");
                System.Diagnostics.Debug.WriteLine($"=== End Level Reference Validation - {stage} ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during level validation: {ex.Message}");
            }
        }

    }
}
