using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;
using GEN.ModelGroupExporter.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Controls;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of IElementTransferService for transferring elements between documents
    /// </summary>
    public class ElementTransferService : IElementTransferService
    {
        // Store MEP system information for use in UpdateElementHosting
        private Dictionary<ElementId, MepSystemInfo> _mepSystemInfo = new Dictionary<ElementId, MepSystemInfo>();
        private Dictionary<ElementId, ElementId> _sourceToTargetElementMapping = new Dictionary<ElementId, ElementId>();
        private Dictionary<ElementId, ElementId> _mepSystemMapping = new Dictionary<ElementId, ElementId>();
        public ICollection<ElementId> CopyElements(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds, Dictionary<ElementId, ElementId> levelMapping)
        {
            return CopyElementsWithTransform(sourceDocument, targetDocument, elementIds, levelMapping, Transform.Identity);
        }

        public ICollection<ElementId> CopyElementsWithTransform(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds, Dictionary<ElementId, ElementId> levelMapping, Transform transform)
        {
            var copiedElementIds = new List<ElementId>();

            try
            {
                // Store MEP system information from source elements before copying
                _mepSystemInfo = CollectMepSystemInfo(sourceDocument, elementIds);

                // Collect MEP systems that need to be copied to maintain connectivity
                var mepSystemIds = CollectMepSystems(sourceDocument, elementIds);
                System.Diagnostics.Debug.WriteLine($"Found {mepSystemIds.Count} MEP systems to copy along with elements");

                // First, copy required types and families
                //var requiredTypes = CopyRequiredTypes(sourceDocument, targetDocument, elementIds);

                // Configure copy options to handle duplicate types silently (preventing pop ups)
                var copyOptions = new CopyPasteOptions();
                copyOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());

                List<ElementId> failedElements = new List<ElementId>();
                List<ElementId> copiedIds = new List<ElementId>();

                using (var trans = new Transaction(targetDocument, "Copy elements"))
                {
                    trans.Start();

                    // First, copy MEP systems to maintain connectivity
                    if (mepSystemIds.Count > 0)
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"Copying {mepSystemIds.Count} MEP systems first...");
                            var copiedSystems = ElementTransformUtils.CopyElements(
                                sourceDocument,
                                mepSystemIds,
                                targetDocument,
                                transform,
                                copyOptions);

                            // Store system mapping for later reference
                            for (int i = 0; i < mepSystemIds.Count && i < copiedSystems.Count; i++)
                            {
                                var sourceSystemId = mepSystemIds.ElementAt(i);
                                var targetSystemId = copiedSystems.ElementAt(i);
                                _mepSystemMapping[sourceSystemId] = targetSystemId;
                                System.Diagnostics.Debug.WriteLine($"Mapped MEP system {sourceSystemId} -> {targetSystemId}");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to copy MEP systems: {ex.Message}");
                        }
                    }

                    // Copy elements in batches to reduce the number of transactions and improve performance
                    var batchSize = 50; // Process elements in batches
                    var elementBatches = elementIds.Select((id, index) => new { id, index })
                                                     .GroupBy(x => x.index / batchSize)
                                                     .Select(g => g.Select(x => x.id).ToList())
                                                     .ToList();

                    foreach (var batch in elementBatches)
                    {
                        try
                        {
                            // Use the provided transform instead of Identity
                            ICollection<ElementId> copied = ElementTransformUtils.CopyElements(
                                sourceDocument,
                                batch,
                                targetDocument,
                                transform,
                                copyOptions);

                            copiedIds.AddRange(copied);

                            // Store source to target element mapping for MEP system info application
                            for (int i = 0; i < batch.Count && i < copied.Count; i++)
                            {
                                var sourceId = batch.ElementAt(i);
                                var targetId = copied.ElementAt(i);
                                _sourceToTargetElementMapping[sourceId] = targetId;
                            }
                        }
                        catch (Exception ex)
                        {
                            // If batch fails, try individual elements
                            System.Diagnostics.Debug.WriteLine($"Batch copy failed, trying individual elements: {ex.Message}");

                            foreach (ElementId id in batch)
                            {
                                try
                                {
                                    ICollection<ElementId> copied = ElementTransformUtils.CopyElements(
                                        sourceDocument,
                                        new List<ElementId> { id },
                                        targetDocument,
                                        transform,
                                        copyOptions);
                                    copiedIds.AddRange(copied);

                                    // Store source to target element mapping for individual elements
                                    if (copied.Count > 0)
                                    {
                                        _sourceToTargetElementMapping[id] = copied.First();
                                    }
                                }
                                catch (Exception individualEx)
                                {
                                    failedElements.Add(id);
                                    System.Diagnostics.Debug.WriteLine($"Element ID {id} failed: {individualEx.Message}");
                                }
                            }
                        }
                    }

                    if (failedElements.Count > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"Failed to copy {failedElements.Count} elements out of {elementIds.Count}");
                    }

                    copiedElementIds.AddRange(copiedIds);
                    trans.Commit();
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to copy elements: {ex.Message}");
                throw;
            }

            return copiedElementIds;
        }

        public ICollection<ElementId> CopyRequiredTypes(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds)
        {
            var copiedTypeIds = new List<ElementId>();
            var typesToCopy = new HashSet<ElementId>();

            // Collect all required types
            foreach (var elementId in elementIds)
            {
                var element = sourceDocument.GetElement(elementId);
                if (element == null) continue;

                // Get element type
                var typeId = element.GetTypeId();
                if (typeId != ElementId.InvalidElementId)
                {
                    typesToCopy.Add(typeId);
                }

                // For family instances, also get the family
                if (element is FamilyInstance familyInstance)
                {
                    var familySymbol = familyInstance.Symbol;
                    if (familySymbol != null)
                    {
                        typesToCopy.Add(familySymbol.Id);

                        // Also add the family
                        var family = familySymbol.Family;
                        if (family != null)
                        {
                            typesToCopy.Add(family.Id);
                        }
                    }
                }
            }

            using (var trans = new Transaction(targetDocument, "Copy Types"))
            {
                trans.Start();
                // Copy types that don't already exist in target document
                foreach (var typeId in typesToCopy)
                {
                    var sourceType = sourceDocument.GetElement(typeId);
                    if (sourceType == null) continue;

                    // Check if type already exists in target document
                    if (!TypeExistsInDocument(targetDocument, sourceType))
                    {
                        try
                        {
                            var copyOptions = new CopyPasteOptions();
                            var copiedIds = ElementTransformUtils.CopyElements(sourceDocument,
                                new List<ElementId> { typeId }, targetDocument, Transform.Identity, copyOptions);

                            copiedTypeIds.AddRange(copiedIds);
                        }
                        catch (System.Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to copy type {sourceType.Name}: {ex.Message}");
                        }
                    }
                }
                trans.Commit();
            }


            return copiedTypeIds;
        }

        public void UpdateElementHosting(Document targetDocument, ICollection<ElementId> copiedElements,
            Dictionary<ElementId, ElementId> levelMapping)
        {
            System.Diagnostics.Debug.WriteLine($"Starting UpdateElementHosting for {copiedElements.Count} elements with {levelMapping.Count} level mappings");

            using (var trans = new Transaction(targetDocument, "Update element hosting"))
            {
                trans.Start();
                try
                {
                    int updatedElements = 0;
                    int elementsWithLevelIssues = 0;

                    foreach (var elementId in copiedElements)
                    {
                        var element = targetDocument.GetElement(elementId);
                        if (element == null)
                        {
                            System.Diagnostics.Debug.WriteLine($"Warning: Element ID {elementId} not found in target document");
                            continue;
                        }

                        bool elementUpdated = false;
                        var elementType = element.GetType().Name;

                        // Update level parameter if element has one
                        var levelParam = element.get_Parameter(BuiltInParameter.LEVEL_PARAM);
                        if (levelParam != null && !levelParam.IsReadOnly)
                        {
                            var currentLevelId = levelParam.AsElementId();

                            if (currentLevelId != ElementId.InvalidElementId)
                            {
                                // Check if the current level exists in target document
                                var currentLevel = targetDocument.GetElement(currentLevelId) as Level;

                                if (currentLevel == null)
                                {
                                    // Level doesn't exist in target document - need to remap
                                    elementsWithLevelIssues++;
                                    System.Diagnostics.Debug.WriteLine($"Element {elementId} ({elementType}) has invalid level reference: {currentLevelId}");

                                    if (levelMapping.ContainsKey(currentLevelId))
                                    {
                                        var newLevelId = levelMapping[currentLevelId];
                                        levelParam.Set(newLevelId);
                                        elementUpdated = true;
                                        System.Diagnostics.Debug.WriteLine($"  -> Remapped to level ID: {newLevelId}");
                                    }
                                    else
                                    {
                                        // Try to find a fallback level
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            levelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                            System.Diagnostics.Debug.WriteLine($"  -> Used fallback level ID: {fallbackLevelId}");
                                        }
                                        else
                                        {
                                            System.Diagnostics.Debug.WriteLine($"  -> No suitable level found for element {elementId}");
                                        }
                                    }
                                }
                                else
                                {
                                    // Level exists in target document - no remapping needed
                                    System.Diagnostics.Debug.WriteLine($"Element {elementId} ({elementType}) level reference is valid: {currentLevel.Name}");
                                }
                            }
                        }

                        // Update reference level parameter
                        var refLevelParam = element.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM);
                        if (refLevelParam != null && !refLevelParam.IsReadOnly)
                        {
                            var currentRefLevelId = refLevelParam.AsElementId();
                            if (currentRefLevelId != ElementId.InvalidElementId)
                            {
                                var currentRefLevel = targetDocument.GetElement(currentRefLevelId) as Level;
                                if (currentRefLevel == null)
                                {
                                    if (levelMapping.ContainsKey(currentRefLevelId))
                                    {
                                        refLevelParam.Set(levelMapping[currentRefLevelId]);
                                        elementUpdated = true;
                                    }
                                    else
                                    {
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentRefLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            refLevelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                        }
                                    }
                                }
                            }
                        }

                        // Update RBS start level parameter (for MEP systems)
                        var rbsStartLevelParam = element.get_Parameter(BuiltInParameter.RBS_START_LEVEL_PARAM);
                        if (rbsStartLevelParam != null && !rbsStartLevelParam.IsReadOnly)
                        {
                            var currentRbsStartLevelId = rbsStartLevelParam.AsElementId();
                            if (currentRbsStartLevelId != ElementId.InvalidElementId)
                            {
                                var currentRbsStartLevel = targetDocument.GetElement(currentRbsStartLevelId) as Level;
                                if (currentRbsStartLevel == null)
                                {
                                    if (levelMapping.ContainsKey(currentRbsStartLevelId))
                                    {
                                        rbsStartLevelParam.Set(levelMapping[currentRbsStartLevelId]);
                                        elementUpdated = true;
                                        System.Diagnostics.Debug.WriteLine($"  -> Updated RBS_START_LEVEL_PARAM for element {elementId}");
                                    }
                                    else
                                    {
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentRbsStartLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            rbsStartLevelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                            System.Diagnostics.Debug.WriteLine($"  -> Used fallback level for RBS_START_LEVEL_PARAM on element {elementId}");
                                        }
                                    }
                                }
                            }
                        }

                        // Update schedule level parameter
                        var scheduleLevelParam = element.get_Parameter(BuiltInParameter.SCHEDULE_LEVEL_PARAM);
                        if (scheduleLevelParam != null && !scheduleLevelParam.IsReadOnly)
                        {
                            var currentScheduleLevelId = scheduleLevelParam.AsElementId();
                            if (currentScheduleLevelId != ElementId.InvalidElementId)
                            {
                                var currentScheduleLevel = targetDocument.GetElement(currentScheduleLevelId) as Level;
                                if (currentScheduleLevel == null)
                                {
                                    if (levelMapping.ContainsKey(currentScheduleLevelId))
                                    {
                                        scheduleLevelParam.Set(levelMapping[currentScheduleLevelId]);
                                        elementUpdated = true;
                                        System.Diagnostics.Debug.WriteLine($"  -> Updated SCHEDULE_LEVEL_PARAM for element {elementId}");
                                    }
                                    else
                                    {
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentScheduleLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            scheduleLevelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                            System.Diagnostics.Debug.WriteLine($"  -> Used fallback level for SCHEDULE_LEVEL_PARAM on element {elementId}");
                                        }
                                    }
                                }
                            }
                        }

                        // Update MEP system parameters if element has undefined values
                        bool mepSystemUpdated = UpdateMepSystemParameters(element, elementId);
                        if (mepSystemUpdated)
                        {
                            elementUpdated = true;
                        }

                        // Reconnect MEP elements to their systems
                        bool reconnected = ReconnectMepElement(element, elementId);
                        if (reconnected)
                        {
                            elementUpdated = true;
                        }

                        if (elementUpdated)
                        {
                            updatedElements++;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"UpdateElementHosting completed: Updated {updatedElements} elements, {elementsWithLevelIssues} had level issues");

                    // Force document regeneration to ensure all changes are applied
                    try
                    {
                        targetDocument.Regenerate();
                        System.Diagnostics.Debug.WriteLine("Document regenerated successfully");
                    }
                    catch (Exception regenEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Document regeneration failed: {regenEx.Message}");
                    }

                    trans.Commit();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to update hosting:\n{ex.Message}");
                    trans.RollBack();
                    throw; // Re-throw to ensure the caller knows about the failure
                }
            }
        }

        public bool ValidateElementTransfer(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> sourceElementIds, ICollection<ElementId> copiedElementIds)
        {
            try
            {
                // Basic validation - check if we have the expected number of elements
                if (copiedElementIds.Count == 0)
                {
                    return false;
                }

                // Validate that copied elements exist and are valid
                foreach (var copiedId in copiedElementIds)
                {
                    var element = targetDocument.GetElement(copiedId);
                    if (element == null || !element.IsValidObject)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public ICollection<ElementId> GetDependentElements(Document document, ICollection<ElementId> elementIds)
        {
            var allElementIds = new HashSet<ElementId>(elementIds);

            foreach (var elementId in elementIds)
            {
                try
                {
                    var element = document.GetElement(elementId);
                    if (element == null) continue;

                    // Get element type if it exists
                    var typeId = element.GetTypeId();
                    if (typeId != ElementId.InvalidElementId)
                    {
                        allElementIds.Add(typeId);
                    }

                    // For family instances, get the family symbol and family
                    if (element is FamilyInstance familyInstance)
                    {
                        var symbol = familyInstance.Symbol;
                        if (symbol != null)
                        {
                            allElementIds.Add(symbol.Id);
                            if (symbol.Family != null)
                            {
                                allElementIds.Add(symbol.Family.Id);
                            }
                        }
                    }

                    // For hosted elements, include the host
                    if (element is FamilyInstance hostedInstance && hostedInstance.Host != null)
                    {
                        allElementIds.Add(hostedInstance.Host.Id);
                    }

                    // For elements with materials, include material dependencies
                    var materialParam = element.get_Parameter(BuiltInParameter.MATERIAL_ID_PARAM);
                    if (materialParam != null && materialParam.AsElementId() != ElementId.InvalidElementId)
                    {
                        allElementIds.Add(materialParam.AsElementId());
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to get dependent elements for {elementId}: {ex.Message}");
                }
            }

            return allElementIds;
        }

        /// <summary>
        /// Finds a fallback level in the target document when direct mapping fails
        /// </summary>
        /// <param name="targetDocument">Target document</param>
        /// <param name="originalLevelId">Original level ID from source document</param>
        /// <param name="levelMapping">Existing level mapping</param>
        /// <returns>Fallback level ID or InvalidElementId if none found</returns>
        private ElementId FindFallbackLevel(Document targetDocument, ElementId originalLevelId,
            Dictionary<ElementId, ElementId> levelMapping)
        {
            try
            {
                // If we have any levels in the target document, use the first available one as fallback
                var targetLevels = new FilteredElementCollector(targetDocument)
                    .OfClass(typeof(Level))
                    .Cast<Level>()
                    .ToList();

                if (targetLevels.Any())
                {
                    // Prefer levels that are already in our mapping (newly created ones)
                    var mappedLevel = levelMapping.Values.FirstOrDefault(levelId =>
                        targetLevels.Any(l => l.Id == levelId));

                    if (mappedLevel != ElementId.InvalidElementId)
                    {
                        return mappedLevel;
                    }

                    // Otherwise, use the first available level
                    return targetLevels.First().Id;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to find fallback level: {ex.Message}");
            }

            return ElementId.InvalidElementId;
        }

        /// <summary>
        /// Collects MEP system information from source elements before copying
        /// </summary>
        /// <param name="sourceDocument">Source document</param>
        /// <param name="elementIds">Element IDs to collect system info from</param>
        /// <returns>Dictionary mapping element IDs to their MEP system information</returns>
        private Dictionary<ElementId, MepSystemInfo> CollectMepSystemInfo(Document sourceDocument, ICollection<ElementId> elementIds)
        {
            var mepSystemInfo = new Dictionary<ElementId, MepSystemInfo>();

            foreach (var elementId in elementIds)
            {
                try
                {
                    var element = sourceDocument.GetElement(elementId);
                    if (element == null) continue;

                    // Check if this is a MEP element by looking for system-related parameters
                    var systemInfo = ExtractMepSystemInfo(element);
                    if (systemInfo != null)
                    {
                        mepSystemInfo[elementId] = systemInfo;
                        System.Diagnostics.Debug.WriteLine($"Collected MEP system info for element {elementId}: SystemType='{systemInfo.SystemTypeName}', SystemClassification='{systemInfo.SystemClassification}'");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to collect MEP system info for element {elementId}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"Collected MEP system info for {mepSystemInfo.Count} elements out of {elementIds.Count} total elements");
            return mepSystemInfo;
        }

        /// <summary>
        /// Extracts MEP system information from an element
        /// Supports: Ducts (RBS_DUCT_SYSTEM_TYPE_PARAM), Pipes (RBS_PIPING_SYSTEM_TYPE_PARAM)
        /// </summary>
        /// <param name="element">The element to extract system info from</param>
        /// <returns>MEP system information or null if not a MEP element</returns>
        private MepSystemInfo ExtractMepSystemInfo(Element element)
        {
            if (element == null) return null;

            var systemInfo = new MepSystemInfo();
            bool hasMepInfo = false;

            // Try to get System Type parameter (stored as ElementId) - different for each MEP category
            var systemTypeParam = element.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
            if (systemTypeParam == null)
            {
                // Try piping system type parameter
                systemTypeParam = element.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
            }

            if (systemTypeParam != null && systemTypeParam.AsElementId() != ElementId.InvalidElementId)
            {
                systemInfo.SystemTypeId = systemTypeParam.AsElementId();
                systemInfo.SystemTypeName = systemTypeParam.AsValueString();
                hasMepInfo = true;
                System.Diagnostics.Debug.WriteLine($"  -> Found SystemType: ID={systemInfo.SystemTypeId}, Name='{systemInfo.SystemTypeName}'");
            }

            // Try to get System Classification parameter
            var systemClassificationParam = element.get_Parameter(BuiltInParameter.RBS_SYSTEM_CLASSIFICATION_PARAM);
            if (systemClassificationParam != null && systemClassificationParam.HasValue)
            {
                systemInfo.SystemClassification = systemClassificationParam.AsValueString();
                hasMepInfo = true;
                System.Diagnostics.Debug.WriteLine($"  -> Found SystemClassification: '{systemInfo.SystemClassification}'");
            }

            return hasMepInfo ? systemInfo : null;
        }

        /// <summary>
        /// Collects MEP systems that need to be copied to maintain element connectivity
        /// </summary>
        /// <param name="sourceDocument">The source document</param>
        /// <param name="elementIds">The elements being copied</param>
        /// <returns>Collection of MEP system IDs to copy</returns>
        private ICollection<ElementId> CollectMepSystems(Document sourceDocument, ICollection<ElementId> elementIds)
        {
            var mepSystemIds = new HashSet<ElementId>();

            try
            {
                foreach (var elementId in elementIds)
                {
                    var element = sourceDocument.GetElement(elementId);
                    if (element == null) continue;

                    // Check if this is a MEP element that belongs to a system
                    if (element is Duct duct)
                    {
                        // Get all duct systems this duct belongs to
                        var systems = duct.DuctSystems;
                        foreach (DuctSystem system in systems)
                        {
                            if (system != null)
                            {
                                mepSystemIds.Add(system.Id);
                                System.Diagnostics.Debug.WriteLine($"Found DuctSystem {system.Id} for duct {elementId}");
                            }
                        }
                    }
                    else if (element is Pipe pipe)
                    {
                        // Get all piping systems this pipe belongs to
                        var systems = pipe.PipingSystems;
                        foreach (PipingSystem system in systems)
                        {
                            if (system != null)
                            {
                                mepSystemIds.Add(system.Id);
                                System.Diagnostics.Debug.WriteLine($"Found PipingSystem {system.Id} for pipe {elementId}");
                            }
                        }
                    }
                    else if (element is FamilyInstance familyInstance)
                    {
                        // Check if this is a MEP fitting or accessory
                        var category = familyInstance.Category;
                        if (category != null)
                        {
                            if (category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctFitting ||
                                category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctAccessory)
                            {
                                // Try to find connected duct systems through connectors
                                var connectorManager = familyInstance.MEPModel?.ConnectorManager;
                                if (connectorManager != null)
                                {
                                    foreach (Connector connector in connectorManager.Connectors)
                                    {
                                        if (connector.MEPSystem is DuctSystem ductSystem)
                                        {
                                            mepSystemIds.Add(ductSystem.Id);
                                            System.Diagnostics.Debug.WriteLine($"Found DuctSystem {ductSystem.Id} for duct fitting {elementId}");
                                        }
                                    }
                                }
                            }
                            else if (category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeFitting ||
                                     category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeAccessory)
                            {
                                // Try to find connected piping systems through connectors
                                var connectorManager = familyInstance.MEPModel?.ConnectorManager;
                                if (connectorManager != null)
                                {
                                    foreach (Connector connector in connectorManager.Connectors)
                                    {
                                        if (connector.MEPSystem is PipingSystem pipingSystem)
                                        {
                                            mepSystemIds.Add(pipingSystem.Id);
                                            System.Diagnostics.Debug.WriteLine($"Found PipingSystem {pipingSystem.Id} for pipe fitting {elementId}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Collected {mepSystemIds.Count} unique MEP systems from {elementIds.Count} elements");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error collecting MEP systems: {ex.Message}");
            }

            return mepSystemIds.ToList();
        }

        /// <summary>
        /// Attempts to reconnect a MEP element to its corresponding system in the target document
        /// </summary>
        /// <param name="targetElement">The copied MEP element</param>
        /// <param name="targetElementId">The element ID in the target document</param>
        /// <returns>True if the element was successfully reconnected</returns>
        private bool ReconnectMepElement(Element targetElement, ElementId targetElementId)
        {
            try
            {
                // Find the corresponding source element ID
                var sourceElementId = _sourceToTargetElementMapping.FirstOrDefault(kvp => kvp.Value == targetElementId).Key;
                if (sourceElementId == ElementId.InvalidElementId)
                {
                    return false;
                }

                // Get the MEP system information for this element
                if (!_mepSystemInfo.ContainsKey(sourceElementId))
                {
                    return false;
                }

                var systemInfo = _mepSystemInfo[sourceElementId];
                System.Diagnostics.Debug.WriteLine($"Attempting to reconnect element {targetElementId} to MEP systems");

                bool reconnected = false;

                // Try to find the corresponding system in the target document
                foreach (var sourceSystemId in _mepSystemMapping.Keys)
                {
                    var targetSystemId = _mepSystemMapping[sourceSystemId];
                    var targetSystem = targetElement.Document.GetElement(targetSystemId);

                    if (targetSystem != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Found target system {targetSystemId} ({targetSystem.GetType().Name})");

                        // Try to add the element to the system
                        if (targetSystem is DuctSystem ductSystem && targetElement is Duct duct)
                        {
                            try
                            {
                                // For duct systems, we might need to use different approaches
                                // The system should automatically include connected elements
                                System.Diagnostics.Debug.WriteLine($"Duct element should be automatically included in DuctSystem {targetSystemId}");
                                reconnected = true;
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Failed to add duct to system: {ex.Message}");
                            }
                        }
                        else if (targetSystem is PipingSystem pipingSystem && targetElement is Pipe pipe)
                        {
                            try
                            {
                                // For piping systems, we might need to use different approaches
                                // The system should automatically include connected elements
                                System.Diagnostics.Debug.WriteLine($"Pipe element should be automatically included in PipingSystem {targetSystemId}");
                                reconnected = true;
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Failed to add pipe to system: {ex.Message}");
                            }
                        }
                    }
                }

                if (reconnected)
                {
                    System.Diagnostics.Debug.WriteLine($"Successfully reconnected element {targetElementId}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Could not reconnect element {targetElementId} to any system");
                }

                return reconnected;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reconnecting MEP element {targetElementId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Updates MEP system parameters for a copied element if they are undefined
        /// </summary>
        /// <param name="targetElement">The copied element in the target document</param>
        /// <param name="targetElementId">The element ID in the target document</param>
        /// <returns>True if any parameters were updated</returns>
        private bool UpdateMepSystemParameters(Element targetElement, ElementId targetElementId)
        {
            if (targetElement == null) return false;

            bool updated = false;

            try
            {
                // Find the corresponding source element ID
                var sourceElementId = _sourceToTargetElementMapping.FirstOrDefault(kvp => kvp.Value == targetElementId).Key;
                if (sourceElementId == ElementId.InvalidElementId || !_mepSystemInfo.ContainsKey(sourceElementId))
                {
                    return false; // No MEP system info available for this element
                }

                var systemInfo = _mepSystemInfo[sourceElementId];
                System.Diagnostics.Debug.WriteLine($"Applying MEP system info to element {targetElementId} from source {sourceElementId}");

                // Update System Type if it's undefined in the target element
                updated |= UpdateSystemTypeParameter(targetElement, systemInfo);

                // Update System Classification if it's undefined in the target element
                updated |= UpdateSystemClassificationParameter(targetElement, systemInfo);

                if (updated)
                {
                    System.Diagnostics.Debug.WriteLine($"  -> Updated MEP system parameters for element {targetElementId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update MEP system parameters for element {targetElementId}: {ex.Message}");
            }

            return updated;
        }

        /// <summary>
        /// Updates the System Type for MEP elements if it's undefined
        /// Uses SetSystemType method for: Duct, Pipe
        /// Uses parameter setting for FamilyInstance fittings/accessories: DuctFitting, DuctAccessory, PipeFitting, PipeAccessory
        /// </summary>
        /// <param name="targetElement">The target element</param>
        /// <param name="systemInfo">The MEP system information from source</param>
        /// <returns>True if the parameter was updated</returns>
        private bool UpdateSystemTypeParameter(Element targetElement, MepSystemInfo systemInfo)
        {
            if (string.IsNullOrEmpty(systemInfo.SystemTypeName)) return false;

            try
            {
                // Try to get System Type parameter - different for each MEP category
                var systemTypeParam = targetElement.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                if (systemTypeParam == null)
                {
                    // Try piping system type parameter
                    systemTypeParam = targetElement.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                }

                if (systemTypeParam != null)
                {
                    var currentValue = systemTypeParam.AsValueString();
                    if (string.IsNullOrEmpty(currentValue) || currentValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                    {
                        // Try to find a matching system type in the target document
                        var targetSystemTypeId = FindSystemTypeByName(targetElement.Document, systemInfo.SystemTypeName);
                        if (targetSystemTypeId != ElementId.InvalidElementId)
                        {
                            // Use the appropriate SetSystemType method based on element type
                            bool success = SetMepElementSystemType(targetElement, targetSystemTypeId);
                            if (success)
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> Set SystemType to '{systemInfo.SystemTypeName}' (ID: {targetSystemTypeId})");
                                return true;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> Failed to set SystemType for element type: {targetElement.GetType().Name}");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"    -> Could not find SystemType '{systemInfo.SystemTypeName}' in target document");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SystemType already has value: '{currentValue}'");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"    -> Failed to update SystemType: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Updates the System Classification parameter if it's undefined
        /// </summary>
        /// <param name="targetElement">The target element</param>
        /// <param name="systemInfo">The MEP system information from source</param>
        /// <returns>True if the parameter was updated</returns>
        private bool UpdateSystemClassificationParameter(Element targetElement, MepSystemInfo systemInfo)
        {
            if (string.IsNullOrEmpty(systemInfo.SystemClassification)) return false;

            try
            {
                var systemClassificationParam = targetElement.get_Parameter(BuiltInParameter.RBS_SYSTEM_CLASSIFICATION_PARAM);
                if (systemClassificationParam != null && !systemClassificationParam.IsReadOnly)
                {
                    var currentValue = systemClassificationParam.AsValueString();
                    if (string.IsNullOrEmpty(currentValue) || currentValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                    {
                        systemClassificationParam.SetValueString(systemInfo.SystemClassification);
                        System.Diagnostics.Debug.WriteLine($"    -> Set SystemClassification to '{systemInfo.SystemClassification}'");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SystemClassification already has value: '{currentValue}'");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"    -> Failed to update SystemClassification: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Sets the system type for a MEP element using the appropriate method based on element type
        /// </summary>
        /// <param name="element">The MEP element</param>
        /// <param name="systemTypeId">The system type ID to set</param>
        /// <returns>True if the system type was set successfully</returns>
        private bool SetMepElementSystemType(Element element, ElementId systemTypeId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"    -> Attempting to set system type {systemTypeId} on {element.GetType().Name} element {element.Id}");

                // Validate the system type exists in the document
                var systemTypeElement = element.Document.GetElement(systemTypeId);
                if (systemTypeElement == null)
                {
                    System.Diagnostics.Debug.WriteLine($"    -> ERROR: System type {systemTypeId} not found in document");
                    return false;
                }
                System.Diagnostics.Debug.WriteLine($"    -> System type found: {systemTypeElement.Name} (Type: {systemTypeElement.GetType().Name})");

                // Handle Duct elements
                if (element is Duct duct)
                {
                    // Check if the system type is compatible with ducts
                    if (!(systemTypeElement is DuctSystemType || systemTypeElement is MEPSystemType))
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> ERROR: System type {systemTypeElement.GetType().Name} is not compatible with Duct elements");
                        return false;
                    }

                    try
                    {
                        duct.SetSystemType(systemTypeId);
                        System.Diagnostics.Debug.WriteLine($"    -> Successfully called SetSystemType for Duct element {element.Id}");

                        // Verify the change was applied
                        var verifyParam = duct.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                        if (verifyParam != null)
                        {
                            var newValue = verifyParam.AsValueString();
                            System.Diagnostics.Debug.WriteLine($"    -> Verification: System type parameter now shows '{newValue}' (ID: {verifyParam.AsElementId()})");

                            // If SetSystemType didn't work, try parameter setting as fallback
                            if (string.IsNullOrEmpty(newValue) || newValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> SetSystemType didn't work, trying parameter setting as fallback");
                                if (!verifyParam.IsReadOnly)
                                {
                                    verifyParam.Set(systemTypeId);
                                    System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() as fallback for Duct element {element.Id}");
                                }
                            }
                        }
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SetSystemType failed for Duct: {ex.Message}, trying parameter setting");
                        // Fallback to parameter setting
                        var param = duct.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                        if (param != null && !param.IsReadOnly)
                        {
                            param.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() fallback for Duct element {element.Id}");
                            return true;
                        }
                        return false;
                    }
                }

                // Handle Pipe elements
                if (element is Pipe pipe)
                {
                    // Check if the system type is compatible with pipes
                    if (!(systemTypeElement is PipingSystemType || systemTypeElement is MEPSystemType))
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> ERROR: System type {systemTypeElement.GetType().Name} is not compatible with Pipe elements");
                        return false;
                    }

                    try
                    {
                        pipe.SetSystemType(systemTypeId);
                        System.Diagnostics.Debug.WriteLine($"    -> Successfully called SetSystemType for Pipe element {element.Id}");

                        // Verify the change was applied
                        var verifyParam = pipe.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                        if (verifyParam != null)
                        {
                            var newValue = verifyParam.AsValueString();
                            System.Diagnostics.Debug.WriteLine($"    -> Verification: System type parameter now shows '{newValue}' (ID: {verifyParam.AsElementId()})");

                            // If SetSystemType didn't work, try parameter setting as fallback
                            if (string.IsNullOrEmpty(newValue) || newValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> SetSystemType didn't work, trying parameter setting as fallback");
                                if (!verifyParam.IsReadOnly)
                                {
                                    verifyParam.Set(systemTypeId);
                                    System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() as fallback for Pipe element {element.Id}");
                                }
                            }
                        }
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SetSystemType failed for Pipe: {ex.Message}, trying parameter setting");
                        // Fallback to parameter setting
                        var param = pipe.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                        if (param != null && !param.IsReadOnly)
                        {
                            param.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() fallback for Pipe element {element.Id}");
                            return true;
                        }
                        return false;
                    }
                }

                // Handle FamilyInstance elements that might be MEP fittings or accessories
                if (element is FamilyInstance familyInstance && familyInstance.Category != null)
                {
                    var category = familyInstance.Category;

                    // Duct fittings and accessories
                    if (category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctFitting ||
                        category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctAccessory)
                    {
                        // Try to set system type using parameter for duct fittings/accessories
                        var systemTypeParam = familyInstance.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                        if (systemTypeParam != null && !systemTypeParam.IsReadOnly)
                        {
                            systemTypeParam.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Set system type for {category.Name} element {element.Id}");
                            return true;
                        }
                    }
                    // Pipe fittings and accessories
                    else if (category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeFitting ||
                             category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeAccessory)
                    {
                        // Try to set system type using parameter for pipe fittings/accessories
                        var systemTypeParam = familyInstance.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                        if (systemTypeParam != null && !systemTypeParam.IsReadOnly)
                        {
                            systemTypeParam.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Set system type for {category.Name} element {element.Id}");
                            return true;
                        }
                    }
                }


                // Element type not supported for SetSystemType
                System.Diagnostics.Debug.WriteLine($"    -> Element type {element.GetType().Name} does not support SetSystemType method");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"    -> Failed to set system type for {element.GetType().Name}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Finds a system type by name in the target document
        /// </summary>
        /// <param name="document">The target document</param>
        /// <param name="systemTypeName">The system type name to find</param>
        /// <returns>ElementId of the matching system type or InvalidElementId if not found</returns>
        private ElementId FindSystemTypeByName(Document document, string systemTypeName)
        {
            try
            {
                // Look for different types of MEP system types that match the name

                // Try MEPSystemType first (general MEP system types)
                var mepSystemTypes = new FilteredElementCollector(document)
                    .OfClass(typeof(MEPSystemType))
                    .Cast<MEPSystemType>()
                    .Where(st => st.Name.Equals(systemTypeName, StringComparison.OrdinalIgnoreCase));

                var matchingMepType = mepSystemTypes.FirstOrDefault();
                if (matchingMepType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found MEPSystemType '{systemTypeName}' with ID: {matchingMepType.Id}");
                    return matchingMepType.Id;
                }

                // Try DuctSystemType for duct systems
                var ductSystemTypes = new FilteredElementCollector(document)
                    .OfClass(typeof(DuctSystemType))
                    .Cast<DuctSystemType>()
                    .Where(st => st.Name.Equals(systemTypeName, StringComparison.OrdinalIgnoreCase));

                var matchingDuctType = ductSystemTypes.FirstOrDefault();
                if (matchingDuctType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found DuctSystemType '{systemTypeName}' with ID: {matchingDuctType.Id}");
                    return matchingDuctType.Id;
                }

                // Try PipingSystemType for piping systems
                var pipingSystemTypes = new FilteredElementCollector(document)
                    .OfClass(typeof(PipingSystemType))
                    .Cast<PipingSystemType>()
                    .Where(st => st.Name.Equals(systemTypeName, StringComparison.OrdinalIgnoreCase));

                var matchingPipingType = pipingSystemTypes.FirstOrDefault();
                if (matchingPipingType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found PipingSystemType '{systemTypeName}' with ID: {matchingPipingType.Id}");
                    return matchingPipingType.Id;
                }

                System.Diagnostics.Debug.WriteLine($"No matching system type found for '{systemTypeName}'");
                return ElementId.InvalidElementId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to find system type '{systemTypeName}': {ex.Message}");
                return ElementId.InvalidElementId;
            }
        }

        private bool TypeExistsInDocument(Document document, Element sourceType)
        {
            try
            {
                if (sourceType is ElementType elementType)
                {
                    // Check if a type with the same name exists
                    var collector = new FilteredElementCollector(document)
                        .OfClass(sourceType.GetType())
                        .Cast<ElementType>()
                        .Where(t => t.Name == elementType.Name);

                    return collector.Any();
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Handler for duplicate type names during copy operations
    /// This prevents the "Duplicate Types" dialog from appearing by automatically resolving conflicts
    /// </summary>
    public class DuplicateTypeNamesHandler : IDuplicateTypeNamesHandler
    {
        public DuplicateTypeAction OnDuplicateTypeNamesFound(DuplicateTypeNamesHandlerArgs args)
        {
            try
            {
                // Log the duplicate type conflict for debugging
                System.Diagnostics.Debug.WriteLine($"Duplicate type conflict resolved: Using destination types for ... types");

                // Always use the destination (target document) types to avoid conflicts
                // This prevents the dialog from appearing and uses the existing types in the new document
                return DuplicateTypeAction.UseDestinationTypes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DuplicateTypeNamesHandler: {ex.Message}");
                // Fallback to using destination types
                return DuplicateTypeAction.UseDestinationTypes;
            }
        }
    }

    /// <summary>
    /// Holds MEP system information for an element
    /// </summary>
    internal class MepSystemInfo
    {
        /// <summary>
        /// System Type ElementId from the source element
        /// </summary>
        public ElementId SystemTypeId { get; set; } = ElementId.InvalidElementId;

        /// <summary>
        /// System Type name (from AsValueString())
        /// </summary>
        public string SystemTypeName { get; set; } = string.Empty;

        /// <summary>
        /// System Classification value
        /// </summary>
        public string SystemClassification { get; set; } = string.Empty;
    }
}
