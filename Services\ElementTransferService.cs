using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;
using GEN.ModelGroupExporter.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Controls;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of IElementTransferService for transferring elements between documents
    /// </summary>
    public class ElementTransferService : IElementTransferService
    {
        // Store MEP system information for use in UpdateElementHosting
        private Dictionary<ElementId, MepSystemInfo> _mepSystemInfo = new Dictionary<ElementId, MepSystemInfo>();
        private Dictionary<ElementId, ElementId> _sourceToTargetElementMapping = new Dictionary<ElementId, ElementId>();
        private Dictionary<ElementId, MepConnectivityInfo> _mepConnectivityInfo = new Dictionary<ElementId, MepConnectivityInfo>();
        public ICollection<ElementId> CopyElements(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds, Dictionary<ElementId, ElementId> levelMapping)
        {
            return CopyElementsWithTransform(sourceDocument, targetDocument, elementIds, levelMapping, Transform.Identity);
        }

        public ICollection<ElementId> CopyElementsWithTransform(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds, Dictionary<ElementId, ElementId> levelMapping, Transform transform)
        {
            var copiedElementIds = new List<ElementId>();

            try
            {
                // Store MEP system information from source elements before copying
                _mepSystemInfo = CollectMepSystemInfo(sourceDocument, elementIds);

                // Store MEP connectivity information for later use
                _mepConnectivityInfo = CollectMepConnectivity(sourceDocument, elementIds);
                System.Diagnostics.Debug.WriteLine($"Found {_mepConnectivityInfo.Count} MEP connectivity relationships to restore");

                // First, copy required types and families
                //var requiredTypes = CopyRequiredTypes(sourceDocument, targetDocument, elementIds);

                // Configure copy options to handle duplicate types silently (preventing pop ups)
                var copyOptions = new CopyPasteOptions();
                copyOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());

                List<ElementId> failedElements = new List<ElementId>();
                List<ElementId> copiedIds = new List<ElementId>();

                using (var trans = new Transaction(targetDocument, "Copy elements"))
                {
                    trans.Start();

                    // Copy elements in batches to reduce the number of transactions and improve performance
                    var batchSize = 50; // Process elements in batches
                    var elementBatches = elementIds.Select((id, index) => new { id, index })
                                                     .GroupBy(x => x.index / batchSize)
                                                     .Select(g => g.Select(x => x.id).ToList())
                                                     .ToList();

                    foreach (var batch in elementBatches)
                    {
                        try
                        {
                            // Use the provided transform instead of Identity
                            ICollection<ElementId> copied = ElementTransformUtils.CopyElements(
                                sourceDocument,
                                batch,
                                targetDocument,
                                transform,
                                copyOptions);

                            copiedIds.AddRange(copied);

                            // Store source to target element mapping for MEP system info application
                            for (int i = 0; i < batch.Count && i < copied.Count; i++)
                            {
                                var sourceId = batch.ElementAt(i);
                                var targetId = copied.ElementAt(i);
                                _sourceToTargetElementMapping[sourceId] = targetId;
                            }
                        }
                        catch (Exception ex)
                        {
                            // If batch fails, try individual elements
                            System.Diagnostics.Debug.WriteLine($"Batch copy failed, trying individual elements: {ex.Message}");

                            foreach (ElementId id in batch)
                            {
                                try
                                {
                                    ICollection<ElementId> copied = ElementTransformUtils.CopyElements(
                                        sourceDocument,
                                        new List<ElementId> { id },
                                        targetDocument,
                                        transform,
                                        copyOptions);
                                    copiedIds.AddRange(copied);

                                    // Store source to target element mapping for individual elements
                                    if (copied.Count > 0)
                                    {
                                        _sourceToTargetElementMapping[id] = copied.First();
                                    }
                                }
                                catch (Exception individualEx)
                                {
                                    failedElements.Add(id);
                                    System.Diagnostics.Debug.WriteLine($"Element ID {id} failed: {individualEx.Message}");
                                }
                            }
                        }
                    }

                    if (failedElements.Count > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"Failed to copy {failedElements.Count} elements out of {elementIds.Count}");
                    }

                    copiedElementIds.AddRange(copiedIds);
                    trans.Commit();
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to copy elements: {ex.Message}");
                throw;
            }

            return copiedElementIds;
        }

        public ICollection<ElementId> CopyRequiredTypes(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds)
        {
            var copiedTypeIds = new List<ElementId>();
            var typesToCopy = new HashSet<ElementId>();

            // Collect all required types
            foreach (var elementId in elementIds)
            {
                var element = sourceDocument.GetElement(elementId);
                if (element == null) continue;

                // Get element type
                var typeId = element.GetTypeId();
                if (typeId != ElementId.InvalidElementId)
                {
                    typesToCopy.Add(typeId);
                }

                // For family instances, also get the family
                if (element is FamilyInstance familyInstance)
                {
                    var familySymbol = familyInstance.Symbol;
                    if (familySymbol != null)
                    {
                        typesToCopy.Add(familySymbol.Id);

                        // Also add the family
                        var family = familySymbol.Family;
                        if (family != null)
                        {
                            typesToCopy.Add(family.Id);
                        }
                    }
                }
            }

            using (var trans = new Transaction(targetDocument, "Copy Types"))
            {
                trans.Start();
                // Copy types that don't already exist in target document
                foreach (var typeId in typesToCopy)
                {
                    var sourceType = sourceDocument.GetElement(typeId);
                    if (sourceType == null) continue;

                    // Check if type already exists in target document
                    if (!TypeExistsInDocument(targetDocument, sourceType))
                    {
                        try
                        {
                            var copyOptions = new CopyPasteOptions();
                            var copiedIds = ElementTransformUtils.CopyElements(sourceDocument,
                                new List<ElementId> { typeId }, targetDocument, Transform.Identity, copyOptions);

                            copiedTypeIds.AddRange(copiedIds);
                        }
                        catch (System.Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to copy type {sourceType.Name}: {ex.Message}");
                        }
                    }
                }
                trans.Commit();
            }


            return copiedTypeIds;
        }

        public void UpdateElementHosting(Document targetDocument, ICollection<ElementId> copiedElements,
            Dictionary<ElementId, ElementId> levelMapping)
        {
            System.Diagnostics.Debug.WriteLine($"Starting UpdateElementHosting for {copiedElements.Count} elements with {levelMapping.Count} level mappings");

            using (var trans = new Transaction(targetDocument, "Update element hosting"))
            {
                trans.Start();
                try
                {
                    int updatedElements = 0;
                    int elementsWithLevelIssues = 0;

                    foreach (var elementId in copiedElements)
                    {
                        var element = targetDocument.GetElement(elementId);
                        if (element == null)
                        {
                            System.Diagnostics.Debug.WriteLine($"Warning: Element ID {elementId} not found in target document");
                            continue;
                        }

                        bool elementUpdated = false;
                        var elementType = element.GetType().Name;

                        // Update level parameter if element has one
                        var levelParam = element.get_Parameter(BuiltInParameter.LEVEL_PARAM);
                        if (levelParam != null && !levelParam.IsReadOnly)
                        {
                            var currentLevelId = levelParam.AsElementId();

                            if (currentLevelId != ElementId.InvalidElementId)
                            {
                                // Check if the current level exists in target document
                                var currentLevel = targetDocument.GetElement(currentLevelId) as Level;

                                if (currentLevel == null)
                                {
                                    // Level doesn't exist in target document - need to remap
                                    elementsWithLevelIssues++;
                                    System.Diagnostics.Debug.WriteLine($"Element {elementId} ({elementType}) has invalid level reference: {currentLevelId}");

                                    if (levelMapping.ContainsKey(currentLevelId))
                                    {
                                        var newLevelId = levelMapping[currentLevelId];
                                        levelParam.Set(newLevelId);
                                        elementUpdated = true;
                                        System.Diagnostics.Debug.WriteLine($"  -> Remapped to level ID: {newLevelId}");
                                    }
                                    else
                                    {
                                        // Try to find a fallback level
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            levelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                            System.Diagnostics.Debug.WriteLine($"  -> Used fallback level ID: {fallbackLevelId}");
                                        }
                                        else
                                        {
                                            System.Diagnostics.Debug.WriteLine($"  -> No suitable level found for element {elementId}");
                                        }
                                    }
                                }
                                else
                                {
                                    // Level exists in target document - no remapping needed
                                    System.Diagnostics.Debug.WriteLine($"Element {elementId} ({elementType}) level reference is valid: {currentLevel.Name}");
                                }
                            }
                        }

                        // Update reference level parameter
                        var refLevelParam = element.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM);
                        if (refLevelParam != null && !refLevelParam.IsReadOnly)
                        {
                            var currentRefLevelId = refLevelParam.AsElementId();
                            if (currentRefLevelId != ElementId.InvalidElementId)
                            {
                                var currentRefLevel = targetDocument.GetElement(currentRefLevelId) as Level;
                                if (currentRefLevel == null)
                                {
                                    if (levelMapping.ContainsKey(currentRefLevelId))
                                    {
                                        refLevelParam.Set(levelMapping[currentRefLevelId]);
                                        elementUpdated = true;
                                    }
                                    else
                                    {
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentRefLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            refLevelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                        }
                                    }
                                }
                            }
                        }

                        // Update RBS start level parameter (for MEP systems)
                        var rbsStartLevelParam = element.get_Parameter(BuiltInParameter.RBS_START_LEVEL_PARAM);
                        if (rbsStartLevelParam != null && !rbsStartLevelParam.IsReadOnly)
                        {
                            var currentRbsStartLevelId = rbsStartLevelParam.AsElementId();
                            if (currentRbsStartLevelId != ElementId.InvalidElementId)
                            {
                                var currentRbsStartLevel = targetDocument.GetElement(currentRbsStartLevelId) as Level;
                                if (currentRbsStartLevel == null)
                                {
                                    if (levelMapping.ContainsKey(currentRbsStartLevelId))
                                    {
                                        rbsStartLevelParam.Set(levelMapping[currentRbsStartLevelId]);
                                        elementUpdated = true;
                                        System.Diagnostics.Debug.WriteLine($"  -> Updated RBS_START_LEVEL_PARAM for element {elementId}");
                                    }
                                    else
                                    {
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentRbsStartLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            rbsStartLevelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                            System.Diagnostics.Debug.WriteLine($"  -> Used fallback level for RBS_START_LEVEL_PARAM on element {elementId}");
                                        }
                                    }
                                }
                            }
                        }

                        // Update schedule level parameter
                        var scheduleLevelParam = element.get_Parameter(BuiltInParameter.SCHEDULE_LEVEL_PARAM);
                        if (scheduleLevelParam != null && !scheduleLevelParam.IsReadOnly)
                        {
                            var currentScheduleLevelId = scheduleLevelParam.AsElementId();
                            if (currentScheduleLevelId != ElementId.InvalidElementId)
                            {
                                var currentScheduleLevel = targetDocument.GetElement(currentScheduleLevelId) as Level;
                                if (currentScheduleLevel == null)
                                {
                                    if (levelMapping.ContainsKey(currentScheduleLevelId))
                                    {
                                        scheduleLevelParam.Set(levelMapping[currentScheduleLevelId]);
                                        elementUpdated = true;
                                        System.Diagnostics.Debug.WriteLine($"  -> Updated SCHEDULE_LEVEL_PARAM for element {elementId}");
                                    }
                                    else
                                    {
                                        var fallbackLevelId = FindFallbackLevel(targetDocument, currentScheduleLevelId, levelMapping);
                                        if (fallbackLevelId != ElementId.InvalidElementId)
                                        {
                                            scheduleLevelParam.Set(fallbackLevelId);
                                            elementUpdated = true;
                                            System.Diagnostics.Debug.WriteLine($"  -> Used fallback level for SCHEDULE_LEVEL_PARAM on element {elementId}");
                                        }
                                    }
                                }
                            }
                        }

                        // Update MEP system parameters if element has undefined values
                        bool mepSystemUpdated = UpdateMepSystemParameters(element, elementId);
                        if (mepSystemUpdated)
                        {
                            elementUpdated = true;
                        }

                        // Note: MEP reconnection will be done after all elements are processed

                        if (elementUpdated)
                        {
                            updatedElements++;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"UpdateElementHosting completed: Updated {updatedElements} elements, {elementsWithLevelIssues} had level issues");

                    // Phase 2: Reconnect MEP elements after all elements are processed
                    System.Diagnostics.Debug.WriteLine("Starting MEP reconnection phase...");
                    int reconnectedElements = 0;

                    foreach (var elementId in copiedElements)
                    {
                        var element = targetDocument.GetElement(elementId);
                        if (element != null)
                        {
                            bool reconnected = ReconnectMepElement(element, elementId);
                            if (reconnected)
                            {
                                reconnectedElements++;
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"MEP reconnection completed: {reconnectedElements} elements reconnected");

                    // Force document regeneration to ensure all changes are applied
                    try
                    {
                        targetDocument.Regenerate();
                        System.Diagnostics.Debug.WriteLine("Document regenerated successfully");
                    }
                    catch (Exception regenEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Document regeneration failed: {regenEx.Message}");
                    }

                    trans.Commit();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to update hosting:\n{ex.Message}");
                    trans.RollBack();
                    throw; // Re-throw to ensure the caller knows about the failure
                }
            }
        }

        public bool ValidateElementTransfer(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> sourceElementIds, ICollection<ElementId> copiedElementIds)
        {
            try
            {
                // Basic validation - check if we have the expected number of elements
                if (copiedElementIds.Count == 0)
                {
                    return false;
                }

                // Validate that copied elements exist and are valid
                foreach (var copiedId in copiedElementIds)
                {
                    var element = targetDocument.GetElement(copiedId);
                    if (element == null || !element.IsValidObject)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public ICollection<ElementId> GetDependentElements(Document document, ICollection<ElementId> elementIds)
        {
            var allElementIds = new HashSet<ElementId>(elementIds);

            foreach (var elementId in elementIds)
            {
                try
                {
                    var element = document.GetElement(elementId);
                    if (element == null) continue;

                    // Get element type if it exists
                    var typeId = element.GetTypeId();
                    if (typeId != ElementId.InvalidElementId)
                    {
                        allElementIds.Add(typeId);
                    }

                    // For family instances, get the family symbol and family
                    if (element is FamilyInstance familyInstance)
                    {
                        var symbol = familyInstance.Symbol;
                        if (symbol != null)
                        {
                            allElementIds.Add(symbol.Id);
                            if (symbol.Family != null)
                            {
                                allElementIds.Add(symbol.Family.Id);
                            }
                        }
                    }

                    // For hosted elements, include the host
                    if (element is FamilyInstance hostedInstance && hostedInstance.Host != null)
                    {
                        allElementIds.Add(hostedInstance.Host.Id);
                    }

                    // For elements with materials, include material dependencies
                    var materialParam = element.get_Parameter(BuiltInParameter.MATERIAL_ID_PARAM);
                    if (materialParam != null && materialParam.AsElementId() != ElementId.InvalidElementId)
                    {
                        allElementIds.Add(materialParam.AsElementId());
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to get dependent elements for {elementId}: {ex.Message}");
                }
            }

            return allElementIds;
        }

        /// <summary>
        /// Finds a fallback level in the target document when direct mapping fails
        /// </summary>
        /// <param name="targetDocument">Target document</param>
        /// <param name="originalLevelId">Original level ID from source document</param>
        /// <param name="levelMapping">Existing level mapping</param>
        /// <returns>Fallback level ID or InvalidElementId if none found</returns>
        private ElementId FindFallbackLevel(Document targetDocument, ElementId originalLevelId,
            Dictionary<ElementId, ElementId> levelMapping)
        {
            try
            {
                // If we have any levels in the target document, use the first available one as fallback
                var targetLevels = new FilteredElementCollector(targetDocument)
                    .OfClass(typeof(Level))
                    .Cast<Level>()
                    .ToList();

                if (targetLevels.Any())
                {
                    // Prefer levels that are already in our mapping (newly created ones)
                    var mappedLevel = levelMapping.Values.FirstOrDefault(levelId =>
                        targetLevels.Any(l => l.Id == levelId));

                    if (mappedLevel != ElementId.InvalidElementId)
                    {
                        return mappedLevel;
                    }

                    // Otherwise, use the first available level
                    return targetLevels.First().Id;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to find fallback level: {ex.Message}");
            }

            return ElementId.InvalidElementId;
        }

        /// <summary>
        /// Collects MEP system information from source elements before copying
        /// </summary>
        /// <param name="sourceDocument">Source document</param>
        /// <param name="elementIds">Element IDs to collect system info from</param>
        /// <returns>Dictionary mapping element IDs to their MEP system information</returns>
        private Dictionary<ElementId, MepSystemInfo> CollectMepSystemInfo(Document sourceDocument, ICollection<ElementId> elementIds)
        {
            var mepSystemInfo = new Dictionary<ElementId, MepSystemInfo>();

            foreach (var elementId in elementIds)
            {
                try
                {
                    var element = sourceDocument.GetElement(elementId);
                    if (element == null) continue;

                    // Check if this is a MEP element by looking for system-related parameters
                    var systemInfo = ExtractMepSystemInfo(element);
                    if (systemInfo != null)
                    {
                        mepSystemInfo[elementId] = systemInfo;
                        System.Diagnostics.Debug.WriteLine($"Collected MEP system info for element {elementId}: SystemType='{systemInfo.SystemTypeName}', SystemClassification='{systemInfo.SystemClassification}'");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to collect MEP system info for element {elementId}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"Collected MEP system info for {mepSystemInfo.Count} elements out of {elementIds.Count} total elements");
            return mepSystemInfo;
        }

        /// <summary>
        /// Extracts MEP system information from an element
        /// Supports: Ducts (RBS_DUCT_SYSTEM_TYPE_PARAM), Pipes (RBS_PIPING_SYSTEM_TYPE_PARAM)
        /// </summary>
        /// <param name="element">The element to extract system info from</param>
        /// <returns>MEP system information or null if not a MEP element</returns>
        private MepSystemInfo ExtractMepSystemInfo(Element element)
        {
            if (element == null) return null;

            var systemInfo = new MepSystemInfo();
            bool hasMepInfo = false;

            // Try to get System Type parameter (stored as ElementId) - different for each MEP category
            var systemTypeParam = element.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
            if (systemTypeParam == null)
            {
                // Try piping system type parameter
                systemTypeParam = element.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
            }

            if (systemTypeParam != null && systemTypeParam.AsElementId() != ElementId.InvalidElementId)
            {
                systemInfo.SystemTypeId = systemTypeParam.AsElementId();
                systemInfo.SystemTypeName = systemTypeParam.AsValueString();
                hasMepInfo = true;
                System.Diagnostics.Debug.WriteLine($"  -> Found SystemType: ID={systemInfo.SystemTypeId}, Name='{systemInfo.SystemTypeName}'");
            }

            // Try to get System Classification parameter
            var systemClassificationParam = element.get_Parameter(BuiltInParameter.RBS_SYSTEM_CLASSIFICATION_PARAM);
            if (systemClassificationParam != null && systemClassificationParam.HasValue)
            {
                systemInfo.SystemClassification = systemClassificationParam.AsValueString();
                hasMepInfo = true;
                System.Diagnostics.Debug.WriteLine($"  -> Found SystemClassification: '{systemInfo.SystemClassification}'");
            }

            return hasMepInfo ? systemInfo : null;
        }

        /// <summary>
        /// Collects MEP connectivity information including physical connections to restore after copying
        /// </summary>
        /// <param name="sourceDocument">The source document</param>
        /// <param name="elementIds">The elements being copied</param>
        /// <returns>Dictionary of connectivity information</returns>
        private Dictionary<ElementId, MepConnectivityInfo> CollectMepConnectivity(Document sourceDocument, ICollection<ElementId> elementIds)
        {
            var connectivityInfo = new Dictionary<ElementId, MepConnectivityInfo>();

            try
            {
                foreach (var elementId in elementIds)
                {
                    var element = sourceDocument.GetElement(elementId);
                    if (element == null) continue;

                    var info = new MepConnectivityInfo();

                    // Check if this is a MEP element and collect its system and connection information
                    if (element is Duct duct)
                    {
                        var connectorManager = duct.ConnectorManager;
                        if (connectorManager != null)
                        {
                            int connectorIndex = 0;
                            foreach (Connector connector in connectorManager.Connectors)
                            {
                                // Collect system information from first connector with system
                                if (connector.MEPSystem != null && string.IsNullOrEmpty(info.SystemName))
                                {
                                    info.SystemName = connector.MEPSystem.Name;
                                    info.SystemTypeName = connector.MEPSystem.GetTypeId() != ElementId.InvalidElementId ?
                                        sourceDocument.GetElement(connector.MEPSystem.GetTypeId())?.Name : null;
                                    System.Diagnostics.Debug.WriteLine($"Duct {elementId} belongs to system '{info.SystemName}' of type '{info.SystemTypeName}'");
                                }

                                // Collect connector information for reconnection
                                var connectorInfo = new ConnectorInfo
                                {
                                    Origin = connector.Origin,
                                    Direction = connector.CoordinateSystem?.BasisZ ?? XYZ.BasisZ,
                                    Shape = connector.Shape,
                                    Radius = connector.Radius,
                                    Width = connector.Width,
                                    Height = connector.Height
                                };

                                // Check if this connector is connected to another element
                                if (connector.IsConnected)
                                {
                                    var connectedConnectors = connector.AllRefs;
                                    foreach (Connector connectedConnector in connectedConnectors)
                                    {
                                        if (connectedConnector.Owner.Id != elementId && elementIds.Contains(connectedConnector.Owner.Id))
                                        {
                                            connectorInfo.ConnectedElementId = connectedConnector.Owner.Id;
                                            // Find the index of the connected connector
                                            var connectedElement = sourceDocument.GetElement(connectedConnector.Owner.Id);
                                            if (connectedElement != null)
                                            {
                                                connectorInfo.ConnectedConnectorIndex = GetConnectorIndex(connectedElement, connectedConnector);
                                            }
                                            System.Diagnostics.Debug.WriteLine($"  -> Connector {connectorIndex} connected to element {connectorInfo.ConnectedElementId}");
                                            break;
                                        }
                                    }
                                }

                                info.Connectors.Add(connectorInfo);
                                connectorIndex++;
                            }
                        }
                    }
                    else if (element is Pipe pipe)
                    {
                        var connectorManager = pipe.ConnectorManager;
                        if (connectorManager != null)
                        {
                            int connectorIndex = 0;
                            foreach (Connector connector in connectorManager.Connectors)
                            {
                                // Collect system information from first connector with system
                                if (connector.MEPSystem != null && string.IsNullOrEmpty(info.SystemName))
                                {
                                    info.SystemName = connector.MEPSystem.Name;
                                    info.SystemTypeName = connector.MEPSystem.GetTypeId() != ElementId.InvalidElementId ?
                                        sourceDocument.GetElement(connector.MEPSystem.GetTypeId())?.Name : null;
                                    System.Diagnostics.Debug.WriteLine($"Pipe {elementId} belongs to system '{info.SystemName}' of type '{info.SystemTypeName}'");
                                }

                                // Collect connector information for reconnection
                                var connectorInfo = new ConnectorInfo
                                {
                                    Origin = connector.Origin,
                                    Direction = connector.CoordinateSystem?.BasisZ ?? XYZ.BasisZ,
                                    Shape = connector.Shape,
                                    Radius = connector.Radius,
                                    Width = connector.Width,
                                    Height = connector.Height
                                };

                                // Check if this connector is connected to another element
                                if (connector.IsConnected)
                                {
                                    var connectedConnectors = connector.AllRefs;
                                    foreach (Connector connectedConnector in connectedConnectors)
                                    {
                                        if (connectedConnector.Owner.Id != elementId && elementIds.Contains(connectedConnector.Owner.Id))
                                        {
                                            connectorInfo.ConnectedElementId = connectedConnector.Owner.Id;
                                            connectorInfo.ConnectedConnectorIndex = GetConnectorIndex(connectedConnector.Owner, connectedConnector);
                                            System.Diagnostics.Debug.WriteLine($"  -> Connector {connectorIndex} connected to element {connectorInfo.ConnectedElementId}");
                                            break;
                                        }
                                    }
                                }

                                info.Connectors.Add(connectorInfo);
                                connectorIndex++;
                            }
                        }
                    }
                    else if (element is FamilyInstance familyInstance)
                    {
                        var category = familyInstance.Category;
                        if (category != null &&
                            (category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctFitting ||
                             category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctAccessory ||
                             category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeFitting ||
                             category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeAccessory))
                        {
                            var connectorManager = familyInstance.MEPModel?.ConnectorManager;
                            if (connectorManager != null)
                            {
                                foreach (Connector connector in connectorManager.Connectors)
                                {
                                    if (connector.MEPSystem != null)
                                    {
                                        info.SystemName = connector.MEPSystem.Name;
                                        info.SystemTypeName = connector.MEPSystem.GetTypeId() != ElementId.InvalidElementId ?
                                            sourceDocument.GetElement(connector.MEPSystem.GetTypeId())?.Name : null;
                                        System.Diagnostics.Debug.WriteLine($"MEP fitting {elementId} belongs to system '{info.SystemName}' of type '{info.SystemTypeName}'");
                                        break; // Take the first system found
                                    }
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(info.SystemName))
                    {
                        connectivityInfo[elementId] = info;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Collected connectivity info for {connectivityInfo.Count} MEP elements");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error collecting MEP connectivity: {ex.Message}");
            }

            return connectivityInfo;
        }

        /// <summary>
        /// Gets the index of a connector within an element's connector manager
        /// </summary>
        /// <param name="element">The element containing the connector</param>
        /// <param name="targetConnector">The connector to find the index for</param>
        /// <returns>The index of the connector, or -1 if not found</returns>
        private int GetConnectorIndex(Element element, Connector targetConnector)
        {
            try
            {
                ConnectorManager connectorManager = null;

                if (element is Duct duct)
                    connectorManager = duct.ConnectorManager;
                else if (element is Pipe pipe)
                    connectorManager = pipe.ConnectorManager;
                else if (element is FamilyInstance familyInstance)
                    connectorManager = familyInstance.MEPModel?.ConnectorManager;

                if (connectorManager != null)
                {
                    int index = 0;
                    foreach (Connector connector in connectorManager.Connectors)
                    {
                        if (connector.Id == targetConnector.Id)
                            return index;
                        index++;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting connector index: {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Recreates MEP connections and sets system types for copied elements
        /// </summary>
        /// <param name="targetElement">The copied MEP element</param>
        /// <param name="targetElementId">The element ID in the target document</param>
        /// <returns>True if connections were successfully recreated</returns>
        private bool ReconnectMepElement(Element targetElement, ElementId targetElementId)
        {
            try
            {
                // Find the corresponding source element ID
                var sourceElementId = _sourceToTargetElementMapping.FirstOrDefault(kvp => kvp.Value == targetElementId).Key;
                if (sourceElementId == ElementId.InvalidElementId)
                {
                    return false;
                }

                // Check if we have connectivity information for this element
                if (!_mepConnectivityInfo.ContainsKey(sourceElementId))
                {
                    return false;
                }

                var connectivityInfo = _mepConnectivityInfo[sourceElementId];
                System.Diagnostics.Debug.WriteLine($"Recreating connections for element {targetElementId}");
                System.Diagnostics.Debug.WriteLine($"  -> Source system: '{connectivityInfo.SystemName}', Type: '{connectivityInfo.SystemTypeName}'");

                bool success = false;

                // Step 1: Set the system type first
                if (!string.IsNullOrEmpty(connectivityInfo.SystemTypeName))
                {
                    var targetSystemTypeId = FindSystemTypeByName(targetElement.Document, connectivityInfo.SystemTypeName);
                    if (targetSystemTypeId != ElementId.InvalidElementId)
                    {
                        bool systemTypeSet = SetMepElementSystemType(targetElement, targetSystemTypeId);
                        if (systemTypeSet)
                        {
                            System.Diagnostics.Debug.WriteLine($"  -> Set system type '{connectivityInfo.SystemTypeName}'");
                            success = true;
                        }
                    }
                }

                // Step 2: Recreate physical connections
                success |= RecreatePhysicalConnections(targetElement, targetElementId, connectivityInfo);

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reconnecting MEP element {targetElementId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Recreates physical connections between MEP elements using Connector.ConnectTo()
        /// </summary>
        /// <param name="targetElement">The target element to connect</param>
        /// <param name="targetElementId">The target element ID</param>
        /// <param name="connectivityInfo">Connection information from source</param>
        /// <returns>True if any connections were successfully recreated</returns>
        private bool RecreatePhysicalConnections(Element targetElement, ElementId targetElementId, MepConnectivityInfo connectivityInfo)
        {
            try
            {
                bool anyConnectionsMade = false;

                // Get the connector manager for the target element
                ConnectorManager targetConnectorManager = null;
                if (targetElement is Duct duct)
                    targetConnectorManager = duct.ConnectorManager;
                else if (targetElement is Pipe pipe)
                    targetConnectorManager = pipe.ConnectorManager;
                else if (targetElement is FamilyInstance familyInstance)
                    targetConnectorManager = familyInstance.MEPModel?.ConnectorManager;

                if (targetConnectorManager == null)
                {
                    System.Diagnostics.Debug.WriteLine($"  -> No connector manager found for element {targetElementId}");
                    return false;
                }

                // Convert connector manager to list for indexing
                var targetConnectors = new List<Connector>();
                foreach (Connector connector in targetConnectorManager.Connectors)
                {
                    targetConnectors.Add(connector);
                }

                System.Diagnostics.Debug.WriteLine($"  -> Found {targetConnectors.Count} connectors on target element");

                // Process each connector from the source connectivity info
                for (int i = 0; i < connectivityInfo.Connectors.Count && i < targetConnectors.Count; i++)
                {
                    var sourceConnectorInfo = connectivityInfo.Connectors[i];
                    var targetConnector = targetConnectors[i];

                    // Skip if this connector wasn't connected in the source
                    if (sourceConnectorInfo.ConnectedElementId == ElementId.InvalidElementId)
                        continue;

                    // Find the corresponding target element that should be connected
                    if (!_sourceToTargetElementMapping.ContainsKey(sourceConnectorInfo.ConnectedElementId))
                        continue;

                    var connectedTargetElementId = _sourceToTargetElementMapping[sourceConnectorInfo.ConnectedElementId];
                    var connectedTargetElement = targetElement.Document.GetElement(connectedTargetElementId);

                    if (connectedTargetElement == null)
                        continue;

                    // Get the connector on the connected element
                    var connectedTargetConnector = GetConnectorByIndex(connectedTargetElement, sourceConnectorInfo.ConnectedConnectorIndex);
                    if (connectedTargetConnector == null)
                        continue;

                    // Check if connectors are compatible and close enough
                    if (AreConnectorsCompatible(targetConnector, connectedTargetConnector, sourceConnectorInfo))
                    {
                        try
                        {
                            // Attempt to connect the connectors
                            targetConnector.ConnectTo(connectedTargetConnector);
                            System.Diagnostics.Debug.WriteLine($"  -> Successfully connected connector {i} to element {connectedTargetElementId}");
                            anyConnectionsMade = true;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"  -> Failed to connect connector {i}: {ex.Message}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"  -> Connectors {i} not compatible or too far apart");
                    }
                }

                return anyConnectionsMade;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error recreating physical connections: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets a connector by its index within an element
        /// </summary>
        /// <param name="element">The element containing the connector</param>
        /// <param name="index">The index of the connector</param>
        /// <returns>The connector at the specified index, or null if not found</returns>
        private Connector GetConnectorByIndex(Element element, int index)
        {
            try
            {
                ConnectorManager connectorManager = null;

                if (element is Duct duct)
                    connectorManager = duct.ConnectorManager;
                else if (element is Pipe pipe)
                    connectorManager = pipe.ConnectorManager;
                else if (element is FamilyInstance familyInstance)
                    connectorManager = familyInstance.MEPModel?.ConnectorManager;

                if (connectorManager != null)
                {
                    int currentIndex = 0;
                    foreach (Connector connector in connectorManager.Connectors)
                    {
                        if (currentIndex == index)
                            return connector;
                        currentIndex++;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting connector by index: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// Checks if two connectors are compatible for connection
        /// </summary>
        /// <param name="connector1">First connector</param>
        /// <param name="connector2">Second connector</param>
        /// <param name="sourceConnectorInfo">Original connector information for validation</param>
        /// <returns>True if connectors can be connected</returns>
        private bool AreConnectorsCompatible(Connector connector1, Connector connector2, ConnectorInfo sourceConnectorInfo)
        {
            try
            {
                // Check if connectors are already connected
                if (connector1.IsConnected && connector2.IsConnected)
                {
                    // Check if they're connected to each other
                    foreach (Connector connectedConnector in connector1.AllRefs)
                    {
                        if (connectedConnector.Id == connector2.Id)
                            return false; // Already connected
                    }
                }

                // Check connector shapes are compatible
                if (connector1.Shape != connector2.Shape)
                {
                    System.Diagnostics.Debug.WriteLine($"    -> Connector shapes don't match: {connector1.Shape} vs {connector2.Shape}");
                    return false;
                }

                // Check connector sizes are compatible (with some tolerance)
                double tolerance = 0.1; // 0.1 feet tolerance
                if (connector1.Shape == ConnectorProfileType.Round)
                {
                    if (Math.Abs(connector1.Radius - connector2.Radius) > tolerance)
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> Round connector radii don't match: {connector1.Radius} vs {connector2.Radius}");
                        return false;
                    }
                }
                else if (connector1.Shape == ConnectorProfileType.Rectangular)
                {
                    if (Math.Abs(connector1.Width - connector2.Width) > tolerance ||
                        Math.Abs(connector1.Height - connector2.Height) > tolerance)
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> Rectangular connector sizes don't match");
                        return false;
                    }
                }

                // Check spatial proximity (connectors should be close to each other)
                double distance = connector1.Origin.DistanceTo(connector2.Origin);
                double maxDistance = 2.0; // 2 feet maximum distance
                if (distance > maxDistance)
                {
                    System.Diagnostics.Debug.WriteLine($"    -> Connectors too far apart: {distance} feet");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking connector compatibility: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Updates MEP system parameters for a copied element if they are undefined
        /// </summary>
        /// <param name="targetElement">The copied element in the target document</param>
        /// <param name="targetElementId">The element ID in the target document</param>
        /// <returns>True if any parameters were updated</returns>
        private bool UpdateMepSystemParameters(Element targetElement, ElementId targetElementId)
        {
            if (targetElement == null) return false;

            bool updated = false;

            try
            {
                // Find the corresponding source element ID
                var sourceElementId = _sourceToTargetElementMapping.FirstOrDefault(kvp => kvp.Value == targetElementId).Key;
                if (sourceElementId == ElementId.InvalidElementId || !_mepSystemInfo.ContainsKey(sourceElementId))
                {
                    return false; // No MEP system info available for this element
                }

                var systemInfo = _mepSystemInfo[sourceElementId];
                System.Diagnostics.Debug.WriteLine($"Applying MEP system info to element {targetElementId} from source {sourceElementId}");

                // Update System Type if it's undefined in the target element
                updated |= UpdateSystemTypeParameter(targetElement, systemInfo);

                // Update System Classification if it's undefined in the target element
                updated |= UpdateSystemClassificationParameter(targetElement, systemInfo);

                if (updated)
                {
                    System.Diagnostics.Debug.WriteLine($"  -> Updated MEP system parameters for element {targetElementId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update MEP system parameters for element {targetElementId}: {ex.Message}");
            }

            return updated;
        }

        /// <summary>
        /// Updates the System Type for MEP elements if it's undefined
        /// Uses SetSystemType method for: Duct, Pipe
        /// Uses parameter setting for FamilyInstance fittings/accessories: DuctFitting, DuctAccessory, PipeFitting, PipeAccessory
        /// </summary>
        /// <param name="targetElement">The target element</param>
        /// <param name="systemInfo">The MEP system information from source</param>
        /// <returns>True if the parameter was updated</returns>
        private bool UpdateSystemTypeParameter(Element targetElement, MepSystemInfo systemInfo)
        {
            if (string.IsNullOrEmpty(systemInfo.SystemTypeName)) return false;

            try
            {
                // Try to get System Type parameter - different for each MEP category
                var systemTypeParam = targetElement.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                if (systemTypeParam == null)
                {
                    // Try piping system type parameter
                    systemTypeParam = targetElement.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                }

                if (systemTypeParam != null)
                {
                    var currentValue = systemTypeParam.AsValueString();
                    if (string.IsNullOrEmpty(currentValue) || currentValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                    {
                        // Try to find a matching system type in the target document
                        var targetSystemTypeId = FindSystemTypeByName(targetElement.Document, systemInfo.SystemTypeName);
                        if (targetSystemTypeId != ElementId.InvalidElementId)
                        {
                            // Use the appropriate SetSystemType method based on element type
                            bool success = SetMepElementSystemType(targetElement, targetSystemTypeId);
                            if (success)
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> Set SystemType to '{systemInfo.SystemTypeName}' (ID: {targetSystemTypeId})");
                                return true;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> Failed to set SystemType for element type: {targetElement.GetType().Name}");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"    -> Could not find SystemType '{systemInfo.SystemTypeName}' in target document");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SystemType already has value: '{currentValue}'");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"    -> Failed to update SystemType: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Updates the System Classification parameter if it's undefined
        /// </summary>
        /// <param name="targetElement">The target element</param>
        /// <param name="systemInfo">The MEP system information from source</param>
        /// <returns>True if the parameter was updated</returns>
        private bool UpdateSystemClassificationParameter(Element targetElement, MepSystemInfo systemInfo)
        {
            if (string.IsNullOrEmpty(systemInfo.SystemClassification)) return false;

            try
            {
                var systemClassificationParam = targetElement.get_Parameter(BuiltInParameter.RBS_SYSTEM_CLASSIFICATION_PARAM);
                if (systemClassificationParam != null && !systemClassificationParam.IsReadOnly)
                {
                    var currentValue = systemClassificationParam.AsValueString();
                    if (string.IsNullOrEmpty(currentValue) || currentValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                    {
                        systemClassificationParam.SetValueString(systemInfo.SystemClassification);
                        System.Diagnostics.Debug.WriteLine($"    -> Set SystemClassification to '{systemInfo.SystemClassification}'");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SystemClassification already has value: '{currentValue}'");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"    -> Failed to update SystemClassification: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Sets the system type for a MEP element using the appropriate method based on element type
        /// </summary>
        /// <param name="element">The MEP element</param>
        /// <param name="systemTypeId">The system type ID to set</param>
        /// <returns>True if the system type was set successfully</returns>
        private bool SetMepElementSystemType(Element element, ElementId systemTypeId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"    -> Attempting to set system type {systemTypeId} on {element.GetType().Name} element {element.Id}");

                // Validate the system type exists in the document
                var systemTypeElement = element.Document.GetElement(systemTypeId);
                if (systemTypeElement == null)
                {
                    System.Diagnostics.Debug.WriteLine($"    -> ERROR: System type {systemTypeId} not found in document");
                    return false;
                }
                System.Diagnostics.Debug.WriteLine($"    -> System type found: {systemTypeElement.Name} (Type: {systemTypeElement.GetType().Name})");

                // Handle Duct elements
                if (element is Duct duct)
                {
                    // Check if the system type is compatible with ducts
                    if (!(systemTypeElement is DuctSystemType || systemTypeElement is MEPSystemType))
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> ERROR: System type {systemTypeElement.GetType().Name} is not compatible with Duct elements");
                        return false;
                    }

                    try
                    {
                        duct.SetSystemType(systemTypeId);
                        System.Diagnostics.Debug.WriteLine($"    -> Successfully called SetSystemType for Duct element {element.Id}");

                        // Verify the change was applied
                        var verifyParam = duct.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                        if (verifyParam != null)
                        {
                            var newValue = verifyParam.AsValueString();
                            System.Diagnostics.Debug.WriteLine($"    -> Verification: System type parameter now shows '{newValue}' (ID: {verifyParam.AsElementId()})");

                            // If SetSystemType didn't work, try parameter setting as fallback
                            if (string.IsNullOrEmpty(newValue) || newValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> SetSystemType didn't work, trying parameter setting as fallback");
                                if (!verifyParam.IsReadOnly)
                                {
                                    verifyParam.Set(systemTypeId);
                                    System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() as fallback for Duct element {element.Id}");
                                }
                            }
                        }
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SetSystemType failed for Duct: {ex.Message}, trying parameter setting");
                        // Fallback to parameter setting
                        var param = duct.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                        if (param != null && !param.IsReadOnly)
                        {
                            param.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() fallback for Duct element {element.Id}");
                            return true;
                        }
                        return false;
                    }
                }

                // Handle Pipe elements
                if (element is Pipe pipe)
                {
                    // Check if the system type is compatible with pipes
                    if (!(systemTypeElement is PipingSystemType || systemTypeElement is MEPSystemType))
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> ERROR: System type {systemTypeElement.GetType().Name} is not compatible with Pipe elements");
                        return false;
                    }

                    try
                    {
                        pipe.SetSystemType(systemTypeId);
                        System.Diagnostics.Debug.WriteLine($"    -> Successfully called SetSystemType for Pipe element {element.Id}");

                        // Verify the change was applied
                        var verifyParam = pipe.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                        if (verifyParam != null)
                        {
                            var newValue = verifyParam.AsValueString();
                            System.Diagnostics.Debug.WriteLine($"    -> Verification: System type parameter now shows '{newValue}' (ID: {verifyParam.AsElementId()})");

                            // If SetSystemType didn't work, try parameter setting as fallback
                            if (string.IsNullOrEmpty(newValue) || newValue.Equals("undefined", StringComparison.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"    -> SetSystemType didn't work, trying parameter setting as fallback");
                                if (!verifyParam.IsReadOnly)
                                {
                                    verifyParam.Set(systemTypeId);
                                    System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() as fallback for Pipe element {element.Id}");
                                }
                            }
                        }
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"    -> SetSystemType failed for Pipe: {ex.Message}, trying parameter setting");
                        // Fallback to parameter setting
                        var param = pipe.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                        if (param != null && !param.IsReadOnly)
                        {
                            param.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Used parameter.Set() fallback for Pipe element {element.Id}");
                            return true;
                        }
                        return false;
                    }
                }

                // Handle FamilyInstance elements that might be MEP fittings or accessories
                if (element is FamilyInstance familyInstance && familyInstance.Category != null)
                {
                    var category = familyInstance.Category;

                    // Duct fittings and accessories
                    if (category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctFitting ||
                        category.Id.IntegerValue == (int)BuiltInCategory.OST_DuctAccessory)
                    {
                        // Try to set system type using parameter for duct fittings/accessories
                        var systemTypeParam = familyInstance.get_Parameter(BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM);
                        if (systemTypeParam != null && !systemTypeParam.IsReadOnly)
                        {
                            systemTypeParam.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Set system type for {category.Name} element {element.Id}");
                            return true;
                        }
                    }
                    // Pipe fittings and accessories
                    else if (category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeFitting ||
                             category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeAccessory)
                    {
                        // Try to set system type using parameter for pipe fittings/accessories
                        var systemTypeParam = familyInstance.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM);
                        if (systemTypeParam != null && !systemTypeParam.IsReadOnly)
                        {
                            systemTypeParam.Set(systemTypeId);
                            System.Diagnostics.Debug.WriteLine($"    -> Set system type for {category.Name} element {element.Id}");
                            return true;
                        }
                    }
                }


                // Element type not supported for SetSystemType
                System.Diagnostics.Debug.WriteLine($"    -> Element type {element.GetType().Name} does not support SetSystemType method");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"    -> Failed to set system type for {element.GetType().Name}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Finds a system type by name in the target document
        /// </summary>
        /// <param name="document">The target document</param>
        /// <param name="systemTypeName">The system type name to find</param>
        /// <returns>ElementId of the matching system type or InvalidElementId if not found</returns>
        private ElementId FindSystemTypeByName(Document document, string systemTypeName)
        {
            try
            {
                // Look for different types of MEP system types that match the name

                // Try MEPSystemType first (general MEP system types)
                var mepSystemTypes = new FilteredElementCollector(document)
                    .OfClass(typeof(MEPSystemType))
                    .Cast<MEPSystemType>()
                    .Where(st => st.Name.Equals(systemTypeName, StringComparison.OrdinalIgnoreCase));

                var matchingMepType = mepSystemTypes.FirstOrDefault();
                if (matchingMepType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found MEPSystemType '{systemTypeName}' with ID: {matchingMepType.Id}");
                    return matchingMepType.Id;
                }

                // Try DuctSystemType for duct systems
                var ductSystemTypes = new FilteredElementCollector(document)
                    .OfClass(typeof(DuctSystemType))
                    .Cast<DuctSystemType>()
                    .Where(st => st.Name.Equals(systemTypeName, StringComparison.OrdinalIgnoreCase));

                var matchingDuctType = ductSystemTypes.FirstOrDefault();
                if (matchingDuctType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found DuctSystemType '{systemTypeName}' with ID: {matchingDuctType.Id}");
                    return matchingDuctType.Id;
                }

                // Try PipingSystemType for piping systems
                var pipingSystemTypes = new FilteredElementCollector(document)
                    .OfClass(typeof(PipingSystemType))
                    .Cast<PipingSystemType>()
                    .Where(st => st.Name.Equals(systemTypeName, StringComparison.OrdinalIgnoreCase));

                var matchingPipingType = pipingSystemTypes.FirstOrDefault();
                if (matchingPipingType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found PipingSystemType '{systemTypeName}' with ID: {matchingPipingType.Id}");
                    return matchingPipingType.Id;
                }

                System.Diagnostics.Debug.WriteLine($"No matching system type found for '{systemTypeName}'");
                return ElementId.InvalidElementId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to find system type '{systemTypeName}': {ex.Message}");
                return ElementId.InvalidElementId;
            }
        }

        private bool TypeExistsInDocument(Document document, Element sourceType)
        {
            try
            {
                if (sourceType is ElementType elementType)
                {
                    // Check if a type with the same name exists
                    var collector = new FilteredElementCollector(document)
                        .OfClass(sourceType.GetType())
                        .Cast<ElementType>()
                        .Where(t => t.Name == elementType.Name);

                    return collector.Any();
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Handler for duplicate type names during copy operations
    /// This prevents the "Duplicate Types" dialog from appearing by automatically resolving conflicts
    /// </summary>
    public class DuplicateTypeNamesHandler : IDuplicateTypeNamesHandler
    {
        public DuplicateTypeAction OnDuplicateTypeNamesFound(DuplicateTypeNamesHandlerArgs args)
        {
            try
            {
                // Log the duplicate type conflict for debugging
                System.Diagnostics.Debug.WriteLine($"Duplicate type conflict resolved: Using destination types for ... types");

                // Always use the destination (target document) types to avoid conflicts
                // This prevents the dialog from appearing and uses the existing types in the new document
                return DuplicateTypeAction.UseDestinationTypes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DuplicateTypeNamesHandler: {ex.Message}");
                // Fallback to using destination types
                return DuplicateTypeAction.UseDestinationTypes;
            }
        }
    }

    /// <summary>
    /// Holds MEP system information for an element
    /// </summary>
    internal class MepSystemInfo
    {
        /// <summary>
        /// System Type ElementId from the source element
        /// </summary>
        public ElementId SystemTypeId { get; set; } = ElementId.InvalidElementId;

        /// <summary>
        /// System Type name (from AsValueString())
        /// </summary>
        public string SystemTypeName { get; set; } = string.Empty;

        /// <summary>
        /// System Classification value
        /// </summary>
        public string SystemClassification { get; set; } = string.Empty;
    }

    /// <summary>
    /// Stores MEP connectivity information for restoring connections after copying
    /// </summary>
    public class MepConnectivityInfo
    {
        /// <summary>
        /// Name of the MEP system this element belongs to
        /// </summary>
        public string SystemName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the system type (e.g., "Supply Air", "Return Air", etc.)
        /// </summary>
        public string SystemTypeName { get; set; } = string.Empty;

        /// <summary>
        /// Information about connectors and their connections
        /// </summary>
        public List<ConnectorInfo> Connectors { get; set; } = new List<ConnectorInfo>();
    }

    /// <summary>
    /// Stores information about a connector and its connections
    /// </summary>
    public class ConnectorInfo
    {
        /// <summary>
        /// Origin point of the connector
        /// </summary>
        public XYZ Origin { get; set; }

        /// <summary>
        /// Direction vector of the connector
        /// </summary>
        public XYZ Direction { get; set; }

        /// <summary>
        /// Element ID that this connector is connected to (if any)
        /// </summary>
        public ElementId ConnectedElementId { get; set; } = ElementId.InvalidElementId;

        /// <summary>
        /// Index of the connector on the connected element
        /// </summary>
        public int ConnectedConnectorIndex { get; set; } = -1;

        /// <summary>
        /// Connector shape (Round, Rectangular, etc.)
        /// </summary>
        public ConnectorProfileType Shape { get; set; }

        /// <summary>
        /// Connector size information
        /// </summary>
        public double Radius { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
    }
}
