﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaCommand;
using GEN.ModelGroupExporter.Services;
using GEN.ModelGroupExporter.Services.Interfaces;
using GEN.ModelGroupExporter.UI.View;
using GEN.ModelGroupExporter.ViewModel;
using System;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace GEN.ModelGroupExporter.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    class ModelGroupExporterCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            var app = uiapp.Application;
            Document doc = uidoc.Document;

            _taskLogger.PreTaskStart();

            try
            {
                // Validate that we have an active document
                if (doc == null)
                {
                    TaskDialog.Show("Model Group Exporter", "No active document found. Please open a Revit project.");
                    return Result.Failed;
                }

                // Configure dependency injection
                var serviceProvider = ServiceContainer.ConfigureServices();

                // Get required services
                var modelGroupService = ServiceContainer.GetRequiredService<IModelGroupService>();
                var exportService = ServiceContainer.GetRequiredService<IExportService>();

                // Create ViewModel
                var viewModel = new ModelGroupExporterViewModel(doc, app, modelGroupService, exportService);

                // Create and show the UI
                var view = new ModelGroupExporterView(viewModel);

                // Show dialog and handle result
                var dialogResult = view.ShowDialog();

                if (dialogResult == true)
                {
                    _taskLogger.Log("Model Group Exporter completed successfully.", LogType.Information);
                    return Result.Succeeded;
                }
                else
                {
                    _taskLogger.Log("Model Group Exporter was cancelled by user.", LogType.Information);
                    return Result.Cancelled;
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
                _taskLogger.Log($"Model Group Exporter failed: {ex.Message}", LogType.Error);
                TaskDialog.Show("Error", $"An error occurred while running the Model Group Exporter:\n\n{ex.Message}");
                return Result.Failed;
            }
            finally
            {
                // Clean up service container
                ServiceContainer.Dispose();
            }
        }

        public override string GetAddinAuthor()
        {
            return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return "Model Group Exporter";
        }

        public override string GetCommandSubName()
        {
            return "Version 2";
        }
    }
}
