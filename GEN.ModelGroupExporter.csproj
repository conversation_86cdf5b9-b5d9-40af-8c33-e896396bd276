﻿<Project Sdk="Microsoft.NET.Sdk">
   <PropertyGroup>
	<UseWPF>true</UseWPF>
	<UseWindowsForms>true</UseWindowsForms>
	<Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
</PropertyGroup>
  <ItemGroup>
    <Compile Remove="UI\ModelessRevitForm\**" />
    <EmbeddedResource Remove="UI\ModelessRevitForm\**" />
    <None Remove="UI\ModelessRevitForm\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="UI\View\BecaLogoBlack.png" />
  </ItemGroup>
    <ItemGroup>
   <ProjectReference Include="..\..\COMMON\BecaCommand\BecaCommand.csproj" />
   <ProjectReference Include="..\..\COMMON\Common.UI.WPF\Common.UI.WPF.csproj" />
   <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
  <ProjectReference Include="..\..\COMMON\Common.UI\Common.UI.csproj" />   
  <ProjectReference Include="..\..\COMMON\Common.Utilities\Common.Utilities.csproj" />
</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Nice3point.Revit.Build.Tasks" Version="2.0.2" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPI" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPIUI" Version="$(RevitVersion).*" />
	</ItemGroup>
	<ItemGroup>
	  <Resource Include="UI\View\BecaLogoBlack.png" />
	</ItemGroup>
</Project>