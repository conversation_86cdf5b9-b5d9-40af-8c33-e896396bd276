using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using System.Collections.Generic;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Service for detecting and managing levels in Revit documents
    /// </summary>
    public interface ILevelService
    {
        /// <summary>
        /// Gets all levels referenced by elements in a model group
        /// </summary>
        /// <param name="document">The source Revit document</param>
        /// <param name="groupElements">Collection of element IDs in the group</param>
        /// <returns>List of levels referenced by the group elements</returns>
        List<LevelModel> GetReferencedLevels(Document document, ICollection<ElementId> groupElements);

        /// <summary>
        /// Creates levels in the target document based on source levels
        /// </summary>
        /// <param name="targetDocument">The target document to create levels in</param>
        /// <param name="sourceLevels">The levels to recreate</param>
        /// <param name="transaction">The active transaction</param>
        /// <returns>Dictionary mapping source level IDs to new level IDs</returns>
        Dictionary<ElementId, ElementId> CreateLevelsInDocument(Document targetDocument, List<LevelModel> sourceLevels);

        /// <summary>
        /// Gets all levels in a document
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <returns>List of all levels in the document</returns>
        List<LevelModel> GetAllLevels(Document document);

        /// <summary>
        /// Finds a level by name and elevation in the target document
        /// </summary>
        /// <param name="document">The target document</param>
        /// <param name="levelName">Name of the level to find</param>
        /// <param name="elevation">Elevation of the level</param>
        /// <param name="tolerance">Tolerance for elevation comparison</param>
        /// <returns>Matching level or null if not found</returns>
        Autodesk.Revit.DB.Level FindMatchingLevel(Document document, string levelName, double elevation, double tolerance = 0.001);

        /// <summary>
        /// Validates that all required levels exist in the target document
        /// </summary>
        /// <param name="targetDocument">The target document</param>
        /// <param name="requiredLevels">The levels that should exist</param>
        /// <returns>True if all levels exist with correct properties</returns>
        bool ValidateLevelsExist(Document targetDocument, List<LevelModel> requiredLevels);
    }
}
