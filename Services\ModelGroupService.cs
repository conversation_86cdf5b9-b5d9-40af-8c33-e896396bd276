using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using GEN.ModelGroupExporter.Services.Interfaces;
using System.Collections.Generic;
using System.Linq;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of IModelGroupService for discovering and analyzing model groups
    /// </summary>
    public class ModelGroupService : IModelGroupService
    {
        private readonly ILevelService _levelService;

        public ModelGroupService(ILevelService levelService)
        {
            _levelService = levelService;
        }

        public List<ModelGroup> GetModelGroups(Document document)
        {
            var modelGroups = new List<ModelGroup>();

            // Get all model group types in the document
            var collector = new FilteredElementCollector(document)
                .OfClass(typeof(GroupType))
                .Cast<GroupType>()
                .Where(gt => gt.get_Parameter(BuiltInParameter.SYMBOL_FAMILY_NAME_PARAM)?.AsString() != "Detail Group");

            int nCount = collector.ToList().Count;
            string progressMessage = "{0} of " + nCount.ToString() + " groups processed...";
            string caption = "Retrieving Model Groups";
            using (Common.UI.Forms.BecaProgressForm pf = new Common.UI.Forms.BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (var groupType in collector)
                {
                    var modelGroup = GetModelGroupDetails(document, groupType.Id);
                    if (modelGroup != null)
                    {
                        modelGroups.Add(modelGroup);
                    }
                }

                pf.Close();
            }

            return modelGroups.OrderBy(g => g.Name).ToList();
        }

        public ModelGroup GetModelGroupDetails(Document document, ElementId groupTypeId)
        {
            var groupType = document.GetElement(groupTypeId) as GroupType;
            if (groupType == null) return null;

            var modelGroup = new ModelGroup
            {
                RevitGroupType = groupType,
                Id = groupType.Id,
                Name = groupType.Name,
                TypeName = groupType.Name
            };

            // Get group elements from the group type definition
            var groupElements = GetGroupElements(document, groupType);
            modelGroup.ElementCount = groupElements.Count;

            // Get referenced levels
            modelGroup.ReferencedLevels = _levelService.GetReferencedLevels(document, groupElements);

            // Get category breakdown
            modelGroup.CategoryBreakdown = AnalyzeCategoryBreakdown(document, groupType);

            return modelGroup;
        }

        public ICollection<ElementId> GetGroupElements(Document document, GroupType groupType)
        {
            var elementIds = new List<ElementId>();
            Group temporaryInstance = null;
            bool isTemporaryInstance = false;

            try
            {
                // First, try to find an existing instance of this group type
                var groupInstance = FindGroupInstance(document, groupType);

                if (groupInstance == null)
                {
                    // No existing instance found - create a temporary one
                    temporaryInstance = CreateTemporaryGroupInstance(document, groupType);
                    groupInstance = temporaryInstance;
                    isTemporaryInstance = true;
                }

                if (groupInstance != null)
                {
                    var memberIds = groupInstance.GetMemberIds();
                    elementIds.AddRange(memberIds);
                    System.Diagnostics.Debug.WriteLine($"Retrieved {memberIds.Count} elements from GroupType '{groupType.Name}' {(isTemporaryInstance ? "(temporary instance)" : "(existing instance)")}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to get instance for GroupType '{groupType.Name}' (ID: {groupType.Id})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting elements for GroupType '{groupType.Name}': {ex.Message}");
            }
            finally
            {
                // Clean up temporary instance if we created one
                if (isTemporaryInstance && temporaryInstance != null)
                {
                    DeleteTemporaryGroupInstance(document, temporaryInstance);
                }
            }

            return elementIds;
        }

        /// <summary>
        /// Gets group elements along with the group origin information for proper transformation
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupType">The GroupType to get elements from</param>
        /// <returns>Tuple containing element IDs and the group origin transformation</returns>
        public (ICollection<ElementId> ElementIds, Transform GroupOriginTransform) GetGroupElementsWithOrigin(Document document, GroupType groupType)
        {
            var elementIds = new List<ElementId>();
            Transform groupOriginTransform = Transform.Identity;
            Group temporaryInstance = null;
            bool isTemporaryInstance = false;

            try
            {
                // First, try to find an existing instance of this group type
                var groupInstance = FindGroupInstance(document, groupType);

                if (groupInstance == null)
                {
                    // No existing instance found - create a temporary one
                    temporaryInstance = CreateTemporaryGroupInstance(document, groupType);
                    groupInstance = temporaryInstance;
                    isTemporaryInstance = true;
                }

                if (groupInstance != null)
                {
                    var memberIds = groupInstance.GetMemberIds();
                    elementIds.AddRange(memberIds);

                    // Get the group's location and origin information
                    var groupLocation = groupInstance.Location;
                    if (groupLocation is LocationPoint locationPoint)
                    {
                        // For groups with point location, create transform from group origin to world origin
                        var groupOrigin = locationPoint.Point;
                        groupOriginTransform = Transform.CreateTranslation(groupOrigin.Negate());
                        System.Diagnostics.Debug.WriteLine($"Group '{groupType.Name}' origin at: {groupOrigin}, transform: {groupOriginTransform}");
                    }
                    else if (groupLocation is LocationCurve locationCurve)
                    {
                        // For groups with curve location (rare), use the start point
                        var groupOrigin = locationCurve.Curve.GetEndPoint(0);
                        groupOriginTransform = Transform.CreateTranslation(groupOrigin.Negate());
                        System.Diagnostics.Debug.WriteLine($"Group '{groupType.Name}' curve origin at: {groupOrigin}, transform: {groupOriginTransform}");
                    }

                    System.Diagnostics.Debug.WriteLine($"Retrieved {memberIds.Count} elements from GroupType '{groupType.Name}' {(isTemporaryInstance ? "(temporary instance)" : "(existing instance)")} with origin transform");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to get instance for GroupType '{groupType.Name}' (ID: {groupType.Id})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting elements with origin for GroupType '{groupType.Name}': {ex.Message}");
            }
            finally
            {
                // Clean up temporary instance if we created one
                if (isTemporaryInstance && temporaryInstance != null)
                {
                    DeleteTemporaryGroupInstance(document, temporaryInstance);
                }
            }

            return (elementIds, groupOriginTransform);
        }

        public Dictionary<string, int> AnalyzeCategoryBreakdown(Document document, GroupType groupType)
        {
            var categoryBreakdown = new Dictionary<string, int>();
            Group temporaryInstance = null;
            bool isTemporaryInstance = false;

            try
            {
                // First, try to find an existing instance
                var groupInstance = FindGroupInstance(document, groupType);

                if (groupInstance == null)
                {
                    // No existing instance found - create a temporary one
                    temporaryInstance = CreateTemporaryGroupInstance(document, groupType);
                    groupInstance = temporaryInstance;
                    isTemporaryInstance = true;
                }

                if (groupInstance != null)
                {
                    var memberIds = groupInstance.GetMemberIds();

                    foreach (var memberId in memberIds)
                    {
                        var element = document.GetElement(memberId);
                        if (element?.Category != null)
                        {
                            var categoryName = element.Category.Name;
                            if (categoryBreakdown.ContainsKey(categoryName))
                            {
                                categoryBreakdown[categoryName]++;
                            }
                            else
                            {
                                categoryBreakdown[categoryName] = 1;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error analyzing category breakdown for GroupType '{groupType.Name}': {ex.Message}");
            }
            finally
            {
                // Clean up temporary instance if we created one
                if (isTemporaryInstance && temporaryInstance != null)
                {
                    DeleteTemporaryGroupInstance(document, temporaryInstance);
                }
            }

            return categoryBreakdown;
        }
        /// <summary>
        /// Finds an instance of the specified GroupType in the document
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupType">The GroupType to find an instance of</param>
        /// <returns>A Group instance of the specified type, or null if none found</returns>
        private Group FindGroupInstance(Document document, GroupType groupType)
        {
            try
            {
                // Find all Group instances in the document that match this GroupType
                var groupInstances = new FilteredElementCollector(document)
                    .OfClass(typeof(Group))
                    .Cast<Group>()
                    .Where(g => g.GroupType.Id == groupType.Id)
                    .ToList();

                if (groupInstances.Any())
                {
                    // Return the first instance found
                    var firstInstance = groupInstances.First();
                    System.Diagnostics.Debug.WriteLine($"Found {groupInstances.Count} instances of GroupType '{groupType.Name}', using first instance (ID: {firstInstance.Id})");
                    return firstInstance;
                }
                else
                {
                    // No instances found - create a temporary instance to get member IDs
                    System.Diagnostics.Debug.WriteLine($"No instances found for GroupType '{groupType.Name}' - creating temporary instance");
                    return CreateTemporaryGroupInstance(document, groupType);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error finding instance for GroupType '{groupType.Name}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a temporary group instance to access member IDs, then deletes it
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="groupType">The GroupType to create an instance of</param>
        /// <returns>A temporary Group instance, or null if creation failed</returns>
        private Group CreateTemporaryGroupInstance(Document document, GroupType groupType)
        {
            Group temporaryInstance = null;

            try
            {
                using (var trans = new Transaction(document, "Create temporary group instance"))
                {
                    trans.Start();

                    try
                    {
                        // Create a temporary instance at origin
                        var location = XYZ.Zero;
                        temporaryInstance = document.Create.PlaceGroup(location, groupType);

                        if (temporaryInstance != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"Created temporary instance (ID: {temporaryInstance.Id}) for GroupType '{groupType.Name}'");
                            trans.Commit();
                            return temporaryInstance;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to create temporary instance for GroupType '{groupType.Name}'");
                            trans.RollBack();
                            return null;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error creating temporary instance for GroupType '{groupType.Name}': {ex.Message}");
                        trans.RollBack();
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Transaction error creating temporary instance for GroupType '{groupType.Name}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Deletes a temporary group instance that was created for accessing member IDs
        /// </summary>
        /// <param name="document">The Revit document</param>
        /// <param name="temporaryInstance">The temporary Group instance to delete</param>
        private void DeleteTemporaryGroupInstance(Document document, Group temporaryInstance)
        {
            try
            {
                using (var trans = new Transaction(document, "Delete temporary group instance"))
                {
                    trans.Start();

                    try
                    {
                        document.Delete(temporaryInstance.Id);
                        System.Diagnostics.Debug.WriteLine($"Deleted temporary group instance (ID: {temporaryInstance.Id})");
                        trans.Commit();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error deleting temporary group instance (ID: {temporaryInstance.Id}): {ex.Message}");
                        trans.RollBack();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Transaction error deleting temporary group instance (ID: {temporaryInstance.Id}): {ex.Message}");
            }
        }

        public BoundingBoxXYZ GetGroupBoundingBox(Document document, Group group)
        {
            try
            {
                // Try to get the group's bounding box directly
                var groupBoundingBox = group.get_BoundingBox(null);
                if (groupBoundingBox != null)
                {
                    return groupBoundingBox;
                }

                // If direct bounding box is not available, calculate from member elements
                var memberIds = group.GetMemberIds();
                if (!memberIds.Any()) return null;

                XYZ min = null;
                XYZ max = null;

                foreach (var memberId in memberIds)
                {
                    var element = document.GetElement(memberId);
                    var elementBoundingBox = element?.get_BoundingBox(null);
                    
                    if (elementBoundingBox != null)
                    {
                        if (min == null)
                        {
                            min = elementBoundingBox.Min;
                            max = elementBoundingBox.Max;
                        }
                        else
                        {
                            min = new XYZ(
                                System.Math.Min(min.X, elementBoundingBox.Min.X),
                                System.Math.Min(min.Y, elementBoundingBox.Min.Y),
                                System.Math.Min(min.Z, elementBoundingBox.Min.Z)
                            );
                            max = new XYZ(
                                System.Math.Max(max.X, elementBoundingBox.Max.X),
                                System.Math.Max(max.Y, elementBoundingBox.Max.Y),
                                System.Math.Max(max.Z, elementBoundingBox.Max.Z)
                            );
                        }
                    }
                }

                if (min != null && max != null)
                {
                    var boundingBox = new BoundingBoxXYZ
                    {
                        Min = min,
                        Max = max
                    };
                    return boundingBox;
                }
            }
            catch (System.Exception)
            {
                // Return null if bounding box calculation fails
            }

            return null;
        }
    }
}
