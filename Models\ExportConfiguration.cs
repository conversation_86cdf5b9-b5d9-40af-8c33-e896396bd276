using System.Collections.Generic;

namespace GEN.ModelGroupExporter.Models
{
    /// <summary>
    /// Configuration settings for the export process
    /// </summary>
    public class ExportConfiguration
    {
        /// <summary>
        /// Base directory where exported files will be saved
        /// </summary>
        public string ExportDirectory { get; set; }

        /// <summary>
        /// File naming pattern for exported files
        /// Available tokens: {GroupName}, {TypeName}, {Date}, {Time}, {Index}
        /// </summary>
        public string FileNamePattern { get; set; }

        /// <summary>
        /// Whether to purge unused elements after export
        /// </summary>
        public bool PurgeUnusedElements { get; set; }

        /// <summary>
        /// Whether to use minimal template for new documents
        /// </summary>
        public bool UseMinimalTemplate { get; set; }

        /// <summary>
        /// Path to custom template file (if not using minimal template)
        /// </summary>
        public string CustomTemplatePath { get; set; }

        /// <summary>
        /// Whether to preserve group structure in exported files
        /// </summary>
        public bool PreserveGroupStructure { get; set; }

        public ExportConfiguration()
        {
            // Set default values
            ExportDirectory = System.Environment.GetFolderPath(System.Environment.SpecialFolder.Desktop);
            FileNamePattern = "_{Date}_{Time}";
            PurgeUnusedElements = true;
            UseMinimalTemplate = true;
            CustomTemplatePath = string.Empty;
            PreserveGroupStructure = false;
        }

        /// <summary>
        /// Generates the file name for a given model group
        /// </summary>
        /// <returns>Generated file name without extension</returns>
        public string GenerateFileNameForCombinedModelGroup()
        {
            var fileName = FileNamePattern
                .Replace("{Date}", System.DateTime.Now.ToString("ddMMyyyy"))
                .Replace("{Time}", System.DateTime.Now.ToString("HHmmss"));

            return fileName;
        }

        /// <summary>
        /// Generates the file name for a given model group
        /// </summary>
        /// <param name="modelGroup">The model group to generate name for</param>
        /// <param name="index">Optional index for duplicate names</param>
        /// <returns>Generated file name without extension</returns>
        public string GenerateFileNameForSingleModelGroup(ModelGroup modelGroup, int index = 0)
        {
            var fileName = FileNamePattern
                .Replace("{GroupName}", SanitizeFileName(modelGroup.Name))
                .Replace("{TypeName}", SanitizeFileName(modelGroup.TypeName))
                .Replace("{Date}", System.DateTime.Now.ToString("ddMMyyyy"))
                .Replace("{Time}", System.DateTime.Now.ToString("HHmmss"))
                .Replace("{Index}", index > 0 ? $"_{index}" : "");

            return fileName;
        }

        /// <summary>
        /// Sanitizes a string to be safe for use as a file name
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Sanitized file name</returns>
        private string SanitizeFileName(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "Unnamed";

            var invalidChars = System.IO.Path.GetInvalidFileNameChars();
            var sanitized = input;
            
            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Replace multiple underscores with single underscore
            while (sanitized.Contains("__"))
            {
                sanitized = sanitized.Replace("__", "_");
            }

            return sanitized.Trim('_');
        }
    }
}
