using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using GEN.ModelGroupExporter.Services.Interfaces;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Implementation of IDocumentService for creating and managing Revit documents
    /// </summary>
    public class DocumentService : IDocumentService
    {
        public Document CreateNewDocument(Autodesk.Revit.ApplicationServices.Application application, ExportConfiguration configuration)
        {
            Document newDocument = null;

            try
            {
                if (configuration.UseMinimalTemplate || string.IsNullOrEmpty(configuration.CustomTemplatePath))
                {
                    // Create document with default template
                    newDocument = application.NewProjectDocument(UnitSystem.Metric);

                    // Clean up default content to minimize type conflicts
                    CleanupDefaultContent(newDocument);
                }
                else if (File.Exists(configuration.CustomTemplatePath))
                {
                    // Create document with custom template
                    newDocument = application.NewProjectDocument(UnitSystem.Metric/*configuration.CustomTemplatePath*/);
                }
                else
                {
                    // Fallback to default template if custom template doesn't exist
                    newDocument = application.NewProjectDocument(UnitSystem.Metric);

                    // Clean up default content to minimize type conflicts
                    CleanupDefaultContent(newDocument);
                }

                System.Diagnostics.Debug.WriteLine($"Created new document. Title: {newDocument.Title}");
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create new document: {ex.Message}");
                throw;
            }

            return newDocument;
        }

        public void SaveDocument(Document document, string filePath, ExportConfiguration configuration)
        {
            try
            {
                // Ensure directory exists
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Delete existing file if it exists (since we can't use OverwriteExistingFile)
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // Configure save options
                var saveAsOptions = new SaveAsOptions();
                // Note: OverwriteExistingFile property doesn't exist in Revit API
                // We handle file overwriting manually above

                // Create ModelPath for the file
                var modelPath = ModelPathUtils.ConvertUserVisiblePathToModelPath(filePath);

                // Save the document
                // Note: SaveAs doesn't require a transaction, it's a document-level operation
                document.SaveAs(modelPath, saveAsOptions);
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save document to {filePath}: {ex.Message}");
                throw;
            }
        }

        public void PurgeUnusedElements(Document document)
        {
#if TargetYear2024 || TargetYear2025 || TargetYear2026
            try
            {
                using (var trans = new Transaction(document, "Purge unused"))
                {
                    try
                    {
                        trans.Start();
                        // Get all unused elements
                        var unusedElementIds = document.GetUnusedElements(new HashSet<ElementId>());

                        if (unusedElementIds.Count > 0)
                        {
                            // Delete unused elements
                            document.Delete(unusedElementIds);
                        }

                        // Additional purging for specific categories
                        PurgeUnusedFamilies(document);
                        PurgeUnusedMaterials(document);
                        PurgeUnusedLinePatterns(document);
                        trans.Commit();
                    }
                    catch (Exception ex)
                    {
                        trans.RollBack();
                    }
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to purge unused elements: {ex.Message}");
                // Don't throw - purging is optional
            }
#else
            MessageBox.Show("PurgeUnusedElements is only supported in Revit 2024 and later.");
#endif
        }

        public double GetDocumentSizeMB(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    return fileInfo.Length / (1024.0 * 1024.0); // Convert bytes to MB
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to get document size for {filePath}: {ex.Message}");
            }

            return 0;
        }

        public bool ValidateDocument(Document document)
        {
            try
            {
                return document != null &&
                       !document.IsReadOnly &&
                       document.IsValidObject;
            }
            catch
            {
                return false;
            }
        }

        public void CloseDocument(Document document, bool saveChanges = false)
        {
            try
            {
                if (document != null && document.IsValidObject)
                {
                    document.Close(saveChanges);
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to close document: {ex.Message}");
            }
        }

        private void PurgeUnusedFamilies(Document document)
        {
            try
            {
                var collector = new FilteredElementCollector(document)
                    .OfClass(typeof(Family));

                var familiesToDelete = new List<ElementId>();

                foreach (Family family in collector)
                {
                    // Check if family has any instances
                    var instanceCollector = new FilteredElementCollector(document)
                        .OfClass(typeof(FamilyInstance))
                        .Where(fi => ((FamilyInstance)fi).Symbol.Family.Id == family.Id);

                    if (!instanceCollector.Any())
                    {
                        familiesToDelete.Add(family.Id);
                    }
                }

                if (familiesToDelete.Count > 0)
                {
                    document.Delete(familiesToDelete);
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to purge unused families: {ex.Message}");
            }
        }

        private void PurgeUnusedMaterials(Document document)
        {
            try
            {
                var collector = new FilteredElementCollector(document)
                    .OfClass(typeof(Material));

                var materialsToDelete = new List<ElementId>();

                foreach (Material material in collector)
                {
                    // Check if material is used by any element
                    var elementCollector = new FilteredElementCollector(document)
                        .WhereElementIsNotElementType()
                        .Where(e => HasMaterial(e, material.Id));

                    if (!elementCollector.Any())
                    {
                        materialsToDelete.Add(material.Id);
                    }
                }

                if (materialsToDelete.Count > 0)
                {
                    document.Delete(materialsToDelete);
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to purge unused materials: {ex.Message}");
            }
        }

        private void PurgeUnusedLinePatterns(Document document)
        {
            try
            {
                var collector = new FilteredElementCollector(document)
                    .OfClass(typeof(LinePatternElement));

                var linePatternsToDelete = new List<ElementId>();

                foreach (LinePatternElement linePattern in collector)
                {
                    // Skip system line patterns
                    if (linePattern.GetLinePattern().Name == "Solid" ||
                        linePattern.GetLinePattern().Name == "Hidden")
                        continue;

                    // Check if line pattern is used
                    // This is a simplified check - in practice, you'd need to check line styles, etc.
                    linePatternsToDelete.Add(linePattern.Id);
                }

                if (linePatternsToDelete.Count > 0)
                {
                    document.Delete(linePatternsToDelete);
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to purge unused line patterns: {ex.Message}");
            }
        }

        private bool HasMaterial(Element element, ElementId materialId)
        {
            try
            {
                var materialParam = element.get_Parameter(BuiltInParameter.MATERIAL_ID_PARAM);
                if (materialParam != null && materialParam.AsElementId() == materialId)
                {
                    return true;
                }

                // Check for structural material
                var structMaterialParam = element.get_Parameter(BuiltInParameter.STRUCTURAL_MATERIAL_PARAM);
                if (structMaterialParam != null && structMaterialParam.AsElementId() == materialId)
                {
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Cleans up default content from a new document to minimize type conflicts
        /// </summary>
        /// <param name="document">The newly created document</param>
        private void CleanupDefaultContent(Document document)
        {
            try
            {
                using (var trans = new Transaction(document, "Cleanup default content"))
                {
                    trans.Start();

                    // Remove default views that might cause conflicts
                    RemoveDefaultViews(document);

                    // Remove default families that might conflict
                    RemoveUnusedDefaultFamilies(document);

                    trans.Commit();
                }

                System.Diagnostics.Debug.WriteLine("Default content cleanup completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to cleanup default content: {ex.Message}");
                // Don't throw - this is optional cleanup
            }
        }

        /// <summary>
        /// Removes default views that are not essential
        /// </summary>
        /// <param name="document">The document to clean</param>
        private void RemoveDefaultViews(Document document)
        {
            try
            {
                var viewsToDelete = new List<ElementId>();
                var collector = new FilteredElementCollector(document)
                    .OfClass(typeof(Autodesk.Revit.DB.View))
                    .Cast<Autodesk.Revit.DB.View>()
                    .Where(v => v.CanBePrinted == false && // Skip views that can't be printed (like browser organization)
                               !v.IsTemplate && // Don't delete view templates
                               v.ViewType != ViewType.ProjectBrowser && // Keep project browser
                               v.ViewType != ViewType.SystemBrowser); // Keep system browser

                foreach (var view in collector)
                {
                    if (view.Id != document.ActiveView.Id) // Don't delete the active view
                    {
                        viewsToDelete.Add(view.Id);
                    }
                }

                if (viewsToDelete.Count > 0)
                {
                    document.Delete(viewsToDelete);
                    System.Diagnostics.Debug.WriteLine($"Removed {viewsToDelete.Count} default views");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to remove default views: {ex.Message}");
            }
        }

        /// <summary>
        /// Removes unused default families to reduce type conflicts
        /// </summary>
        /// <param name="document">The document to clean</param>
        private void RemoveUnusedDefaultFamilies(Document document)
        {
#if TargetYear2024 || TargetYear2025 || TargetYear2026

            try
            {
                // Get unused elements (this includes unused families)
                var unusedElements = document.GetUnusedElements(new HashSet<ElementId>());

                if (unusedElements.Count > 0)
                {
                    document.Delete(unusedElements);
                    System.Diagnostics.Debug.WriteLine($"Removed {unusedElements.Count} unused default elements");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to remove unused default families: {ex.Message}");
            }
#else
            MessageBox.Show("RemoveUnusedDefaultFamilies is only supported in Revit 2024 and later.");
#endif
        }
    }
}
