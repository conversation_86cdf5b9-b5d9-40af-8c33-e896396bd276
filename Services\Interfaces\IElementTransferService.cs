using Autodesk.Revit.DB;
using System.Collections.Generic;

namespace GEN.ModelGroupExporter.Services.Interfaces
{
    /// <summary>
    /// Service for transferring elements between Revit documents
    /// </summary>
    public interface IElementTransferService
    {
        /// <summary>
        /// Copies elements from source document to target document
        /// </summary>
        /// <param name="sourceDocument">The source document</param>
        /// <param name="targetDocument">The target document</param>
        /// <param name="elementIds">Collection of element IDs to copy</param>
        /// <param name="levelMapping">Mapping of source level IDs to target level IDs</param>
        /// <param name="transaction">The active transaction</param>
        /// <returns>Collection of copied element IDs in the target document</returns>
        ICollection<ElementId> CopyElements(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds, Dictionary<ElementId, ElementId> levelMapping);

        /// <summary>
        /// Copies elements from source document to target document with a specific transformation
        /// </summary>
        /// <param name="sourceDocument">The source document</param>
        /// <param name="targetDocument">The target document</param>
        /// <param name="elementIds">Collection of element IDs to copy</param>
        /// <param name="levelMapping">Mapping of source level IDs to target level IDs</param>
        /// <param name="transform">Transformation to apply during copy</param>
        /// <returns>Collection of copied element IDs in the target document</returns>
        ICollection<ElementId> CopyElementsWithTransform(Document sourceDocument, Document targetDocument,
            ICollection<ElementId> elementIds, Dictionary<ElementId, ElementId> levelMapping, Transform transform);

        /// <summary>
        /// Copies types and families required by the elements
        /// </summary>
        /// <param name="sourceDocument">The source document</param>
        /// <param name="targetDocument">The target document</param>
        /// <param name="elementIds">Collection of element IDs that need their types</param>
        /// <returns>Collection of copied type IDs</returns>
        ICollection<ElementId> CopyRequiredTypes(Document sourceDocument, Document targetDocument, 
            ICollection<ElementId> elementIds);

        /// <summary>
        /// Updates element hosting relationships after copying
        /// </summary>
        /// <param name="targetDocument">The target document</param>
        /// <param name="copiedElements">Collection of copied element IDs</param>
        /// <param name="levelMapping">Mapping of source level IDs to target level IDs</param>
        /// <param name="transaction">The active transaction</param>
        void UpdateElementHosting(Document targetDocument, ICollection<ElementId> copiedElements, 
            Dictionary<ElementId, ElementId> levelMapping);

        /// <summary>
        /// Validates that elements were copied correctly
        /// </summary>
        /// <param name="sourceDocument">The source document</param>
        /// <param name="targetDocument">The target document</param>
        /// <param name="sourceElementIds">Original element IDs</param>
        /// <param name="copiedElementIds">Copied element IDs</param>
        /// <returns>True if all elements were copied successfully</returns>
        bool ValidateElementTransfer(Document sourceDocument, Document targetDocument, 
            ICollection<ElementId> sourceElementIds, ICollection<ElementId> copiedElementIds);

        /// <summary>
        /// Gets all dependent elements that need to be copied with the main elements
        /// </summary>
        /// <param name="document">The source document</param>
        /// <param name="elementIds">Main element IDs</param>
        /// <returns>Collection of all element IDs including dependencies</returns>
        ICollection<ElementId> GetDependentElements(Document document, ICollection<ElementId> elementIds);
    }
}
