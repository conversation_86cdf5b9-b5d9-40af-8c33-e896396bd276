using Autodesk.Revit.DB;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GEN.ModelGroupExporter.Models;
using GEN.ModelGroupExporter.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows.Data;

namespace GEN.ModelGroupExporter.ViewModel
{
    /// <summary>
    /// ViewModel for the Model Group Exporter UI
    /// </summary>
    public partial class ModelGroupExporterViewModel : ObservableObject
    {
        private readonly IModelGroupService _modelGroupService;
        private readonly IExportService _exportService;
        private readonly Document _document;
        private readonly Autodesk.Revit.ApplicationServices.Application _application;

        [ObservableProperty]
        private ObservableCollection<ModelGroup> modelGroups;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private ExportConfiguration configuration;

        // Collection view for filtering
        private ICollectionView _modelGroupsView;

        // Action to close the window
        public Action CloseWindow { get; set; }

        [ObservableProperty]
        private bool isExporting;

        [ObservableProperty]
        private string statusMessage;

        [ObservableProperty]
        private double progressValue;

        [ObservableProperty]
        private string progressText;

        [ObservableProperty]
        private bool canExport;



        public ICollectionView ModelGroupsView => _modelGroupsView;

        public List<ModelGroup> SelectedGroups => ModelGroups?.Where(g => g.IsSelected).ToList() ?? new List<ModelGroup>();

        public int SelectedGroupCount => SelectedGroups.Count;

        public bool HasSearchText => !string.IsNullOrWhiteSpace(SearchText);

        public ModelGroupExporterViewModel(Document document, Autodesk.Revit.ApplicationServices.Application application,
            IModelGroupService modelGroupService, IExportService exportService)
        {
            _document = document;
            _application = application;
            _modelGroupService = modelGroupService;
            _exportService = exportService;

            // Initialize properties
            Configuration = new ExportConfiguration();
            ModelGroups = new ObservableCollection<ModelGroup>();
            StatusMessage = "Ready";
            CanExport = false;

            // Initialize collection view for filtering
            _modelGroupsView = CollectionViewSource.GetDefaultView(ModelGroups);
            _modelGroupsView.Filter = FilterModelGroups;

            // Load model groups
            RefreshModelGroups();
        }

        /// <summary>
        /// Called when SearchText property changes to refresh the filter
        /// </summary>
        partial void OnSearchTextChanged(string value)
        {
            _modelGroupsView?.Refresh();
            OnPropertyChanged(nameof(HasSearchText));
        }

        /// <summary>
        /// Filter method for the ModelGroups collection view
        /// </summary>
        /// <param name="item">The item to filter</param>
        /// <returns>True if the item should be visible</returns>
        private bool FilterModelGroups(object item)
        {
            if (item is ModelGroup modelGroup)
            {
                // If search text is empty, show all items
                if (string.IsNullOrWhiteSpace(SearchText))
                    return true;

                // Filter by group name (case-insensitive)
                return modelGroup.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        [RelayCommand]
        private void RefreshModelGroups()
        {
            try
            {
                StatusMessage = "Loading model groups...";
                var groups = _modelGroupService.GetModelGroups(_document);

                ModelGroups.Clear();
                foreach (var group in groups)
                {
                    group.PropertyChanged += OnModelGroupPropertyChanged;
                    ModelGroups.Add(group);
                }

                StatusMessage = $"Found {groups.Count} model groups";
                UpdateCanExport();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading groups: {ex.Message}";
            }
        }

        private void OnModelGroupPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ModelGroup.IsSelected))
            {
                OnPropertyChanged(nameof(SelectedGroupCount));
                UpdateCanExport();
            }
        }

        [RelayCommand]
        private void SelectAll()
        {
            // Select only the filtered/visible groups
            foreach (var item in _modelGroupsView)
            {
                if (item is ModelGroup group)
                {
                    group.IsSelected = true;
                }
            }
        }

        [RelayCommand]
        private void SelectNone()
        {
            // Deselect only the filtered/visible groups
            foreach (var item in _modelGroupsView)
            {
                if (item is ModelGroup group)
                {
                    group.IsSelected = false;
                }
            }
        }

        [RelayCommand]
        private void ClearSearch()
        {
            SearchText = string.Empty;
        }

        [RelayCommand]
        private void BrowseExportDirectory()
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "Select export directory",
                SelectedPath = Configuration.ExportDirectory
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                Configuration.ExportDirectory = dialog.SelectedPath;
                OnPropertyChanged(nameof(Configuration));
            }
        }

        [RelayCommand(CanExecute = nameof(CanExecuteExport))]
        private void ExportSelectedGroups()
        {
            var selectedGroups = SelectedGroups;
            if (selectedGroups.Count == 0)
            {
                StatusMessage = "No groups selected for export";
                return;
            }

            try
            {
                IsExporting = true;
                StatusMessage = $"Exporting {selectedGroups.Count} groups to a single document...";
                ProgressValue = 0;
                ProgressText = "Starting export...";

                var results = _exportService.ExportModelGroups(_document, _application, selectedGroups, Configuration);

                var result = results.FirstOrDefault();
                if (result != null && result.Success)
                {
                    StatusMessage = $"Export completed successfully! {selectedGroups.Count} groups exported to: {Path.GetFileName(result.FilePath)}";
                    ProgressText = $"Exported {result.ElementCount} elements ({result.FileSizeMB:F1} MB)";

                    ProgressValue = 100;
                    MessageBox.Show($"{StatusMessage}", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Close the window after successful export
                    CloseWindow?.Invoke();
                }
                else
                {
                    StatusMessage = $"Export failed: {result?.ErrorMessage ?? "Unknown error"}";
                    ProgressText = "Export failed";
                    ProgressValue = 100;
                    MessageBox.Show($"{StatusMessage}", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Export failed: {ex.Message}";
                ProgressValue = 0;
                ProgressText = "Export failed";
            }
            finally
            {
                IsExporting = false;
            }
        }

        [RelayCommand(CanExecute = nameof(CanExecuteCancel))]
        private void Cancel()
        {
            //_exportService.CancelExport();
            StatusMessage = "Export cancelled";
            IsExporting = false;
        }

        private bool CanExecuteCancel()
        {
            return IsExporting;
        }

        private bool CanExecuteExport()
        {
            return !IsExporting && SelectedGroupCount > 0 &&
                   !string.IsNullOrEmpty(Configuration?.ExportDirectory) &&
                   Directory.Exists(Configuration.ExportDirectory);
        }

        private void UpdateCanExport()
        {
            CanExport = CanExecuteExport();
            ExportSelectedGroupsCommand.NotifyCanExecuteChanged();
        }
    }
}
