using Microsoft.Extensions.DependencyInjection;
using GEN.ModelGroupExporter.Validations;
using System;
using GEN.ModelGroupExporter.Services.Interfaces;

namespace GEN.ModelGroupExporter.Services
{
    /// <summary>
    /// Service container for dependency injection
    /// </summary>
    public static class ServiceContainer
    {
        private static IServiceProvider _serviceProvider;

        /// <summary>
        /// Configures and builds the service container
        /// </summary>
        /// <returns>Configured service provider</returns>
        public static IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();

            // Register services
            services.AddTransient<IModelGroupService, ModelGroupService>();
            services.AddTransient<ILevelService, LevelService>();
            services.AddTransient<IDocumentService, DocumentService>();
            services.AddTransient<IElementTransferService, ElementTransferService>();
            services.AddTransient<IExportService, ExportService>();
            services.AddTransient<IFileManagementService, FileManagementService>();
            services.AddTransient<IExportValidator, ExportValidator>();

            _serviceProvider = services.BuildServiceProvider();
            return _serviceProvider;
        }

        /// <summary>
        /// Gets a service of the specified type
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        public static T GetService<T>()
        {
            if (_serviceProvider == null)
            {
                ConfigureServices();
            }

            return _serviceProvider.GetService<T>();
        }

        /// <summary>
        /// Gets a required service of the specified type
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        /// <exception cref="InvalidOperationException">Thrown if service is not registered</exception>
        public static T GetRequiredService<T>()
        {
            if (_serviceProvider == null)
            {
                ConfigureServices();
            }

            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Disposes the service provider
        /// </summary>
        public static void Dispose()
        {
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
            _serviceProvider = null;
        }
    }
}
