using Autodesk.Revit.DB;
using GEN.ModelGroupExporter.Models;
using GEN.ModelGroupExporter.Services;
using GEN.ModelGroupExporter.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace GEN.ModelGroupExporter.Validations
{
    /// <summary>
    /// Result of export validation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; }
        public List<string> Warnings { get; set; }
        public Dictionary<string, object> Metrics { get; set; }

        public ValidationResult()
        {
            IsValid = true;
            Errors = new List<string>();
            Warnings = new List<string>();
            Metrics = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Service for validating export operations and results
    /// </summary>
    public interface IExportValidator
    {
        /// <summary>
        /// Validates export configuration before starting export
        /// </summary>
        /// <param name="configuration">Configuration to validate</param>
        /// <returns>Validation result</returns>
        ValidationResult ValidateConfiguration(ExportConfiguration configuration);

        /// <summary>
        /// Validates a model group before export
        /// </summary>
        /// <param name="document">Source document</param>
        /// <param name="modelGroup">Model group to validate</param>
        /// <returns>Validation result</returns>
        ValidationResult ValidateModelGroup(Document document, ModelGroup modelGroup);

        /// <summary>
        /// Validates the result of an export operation
        /// </summary>
        /// <param name="sourceDocument">Source document</param>
        /// <param name="exportedFilePath">Path to exported file</param>
        /// <param name="originalGroup">Original model group</param>
        /// <param name="configuration">Export configuration</param>
        /// <returns>Validation result</returns>
        ValidationResult ValidateExportResult(Document sourceDocument, string exportedFilePath,
            ModelGroup originalGroup, ExportConfiguration configuration);

        /// <summary>
        /// Validates level fidelity between source and target documents
        /// </summary>
        /// <param name="sourceDocument">Source document</param>
        /// <param name="targetDocument">Target document</param>
        /// <param name="expectedLevels">Expected levels</param>
        /// <returns>Validation result</returns>
        ValidationResult ValidateLevelFidelity(Document sourceDocument, Document targetDocument,
            List<LevelModel> expectedLevels);
    }

    /// <summary>
    /// Implementation of IExportValidator
    /// </summary>
    public class ExportValidator : IExportValidator
    {
        private readonly ILevelService _levelService;
        private readonly IFileManagementService _fileManagementService;

        public ExportValidator(ILevelService levelService, IFileManagementService fileManagementService)
        {
            _levelService = levelService;
            _fileManagementService = fileManagementService;
        }

        public ValidationResult ValidateConfiguration(ExportConfiguration configuration)
        {
            var result = new ValidationResult();

            if (configuration == null)
            {
                result.IsValid = false;
                result.Errors.Add("Configuration is null");
                return result;
            }

            // Validate export directory
            if (string.IsNullOrEmpty(configuration.ExportDirectory))
            {
                result.IsValid = false;
                result.Errors.Add("Export directory is not specified");
            }
            else
            {
                try
                {
                    if (!Directory.Exists(configuration.ExportDirectory))
                    {
                        Directory.CreateDirectory(configuration.ExportDirectory);
                    }

                    if (!_fileManagementService.ValidateDirectoryWritable(configuration.ExportDirectory))
                    {
                        result.IsValid = false;
                        result.Errors.Add("Export directory is not writable");
                    }

                    var availableSpace = _fileManagementService.GetAvailableDiskSpaceMB(configuration.ExportDirectory);
                    if (availableSpace >= 0 && availableSpace < 100) // Less than 100MB
                    {
                        result.Warnings.Add($"Low disk space: {availableSpace} MB available");
                    }
                }
                catch (Exception ex)
                {
                    result.IsValid = false;
                    result.Errors.Add($"Invalid export directory: {ex.Message}");
                }
            }

            // Validate file name pattern
            if (string.IsNullOrEmpty(configuration.FileNamePattern))
            {
                result.IsValid = false;
                result.Errors.Add("File name pattern is not specified");
            }

            // Validate custom template path
            if (!configuration.UseMinimalTemplate && !string.IsNullOrEmpty(configuration.CustomTemplatePath))
            {
                if (!File.Exists(configuration.CustomTemplatePath))
                {
                    result.IsValid = false;
                    result.Errors.Add("Custom template file does not exist");
                }
            }

            return result;
        }

        public ValidationResult ValidateModelGroup(Document document, ModelGroup modelGroup)
        {
            var result = new ValidationResult();

            if (modelGroup == null)
            {
                result.IsValid = false;
                result.Errors.Add("Model group is null");
                return result;
            }

            if (modelGroup.RevitGroupType == null)
            {
                result.IsValid = false;
                result.Errors.Add("Revit group reference is null");
                return result;
            }

            // Validate group exists in document
            var groupElement = document.GetElement(modelGroup.Id);
            if (groupElement == null)
            {
                result.IsValid = false;
                result.Errors.Add("Group does not exist in document");
                return result;
            }

            // Validate group has elements
            if (modelGroup.ElementCount == 0)
            {
                result.Warnings.Add("Group contains no elements");
            }

            // Validate referenced levels
            if (modelGroup.ReferencedLevels == null || modelGroup.ReferencedLevels.Count == 0)
            {
                result.Warnings.Add("Group has no referenced levels");
            }

            // Add metrics
            result.Metrics["ElementCount"] = modelGroup.ElementCount;
            result.Metrics["LevelCount"] = modelGroup.ReferencedLevels?.Count ?? 0;
            result.Metrics["CategoryCount"] = modelGroup.CategoryBreakdown?.Count ?? 0;

            return result;
        }

        public ValidationResult ValidateExportResult(Document sourceDocument, string exportedFilePath,
            ModelGroup originalGroup, ExportConfiguration configuration)
        {
            var result = new ValidationResult();

            // Validate file exists
            if (!File.Exists(exportedFilePath))
            {
                result.IsValid = false;
                result.Errors.Add("Exported file does not exist");
                return result;
            }

            // Validate file size
            var fileInfo = new FileInfo(exportedFilePath);
            var fileSizeMB = fileInfo.Length / (1024.0 * 1024.0);
            result.Metrics["FileSizeMB"] = fileSizeMB;

            if (fileSizeMB == 0)
            {
                result.IsValid = false;
                result.Errors.Add("Exported file is empty");
                return result;
            }

            // Try to open and validate the exported document
            try
            {
                // Note: In a real implementation, you would need to open the document
                // and validate its contents, but this requires careful handling of
                // Revit's document management
                result.Metrics["FileValidated"] = true;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Cannot validate exported file: {ex.Message}");
            }

            return result;
        }

        public ValidationResult ValidateLevelFidelity(Document sourceDocument, Document targetDocument,
            List<LevelModel> expectedLevels)
        {
            var result = new ValidationResult();

            if (expectedLevels == null || expectedLevels.Count == 0)
            {
                result.Warnings.Add("No levels expected for validation");
                return result;
            }

            var targetLevels = _levelService.GetAllLevels(targetDocument);
            var matchedLevels = 0;

            foreach (var expectedLevel in expectedLevels)
            {
                var matchingLevel = targetLevels.FirstOrDefault(l =>
                    l.Name == expectedLevel.Name &&
                    Math.Abs(l.Elevation - expectedLevel.Elevation) < 0.001);

                if (matchingLevel != null)
                {
                    matchedLevels++;
                }
                else
                {
                    result.Errors.Add($"Level '{expectedLevel.Name}' (Elev: {expectedLevel.Elevation:F2}) not found in target document");
                }
            }

            result.Metrics["ExpectedLevels"] = expectedLevels.Count;
            result.Metrics["MatchedLevels"] = matchedLevels;
            result.Metrics["LevelFidelityPercent"] = expectedLevels.Count > 0 ?
                (double)matchedLevels / expectedLevels.Count * 100 : 100;

            if (matchedLevels < expectedLevels.Count)
            {
                result.IsValid = false;
            }

            return result;
        }
    }
}
