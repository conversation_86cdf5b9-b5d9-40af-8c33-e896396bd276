using GEN.ModelGroupExporter.ViewModel;
using System.Windows;

namespace GEN.ModelGroupExporter.UI.View
{
    /// <summary>
    /// Interaction logic for ModelGroupExporterView.xaml
    /// </summary>
    public partial class ModelGroupExporterView : Window
    {
        public ModelGroupExporterViewModel ViewModel { get; private set; }

        public ModelGroupExporterView()
        {
            InitializeComponent();
        }

        public ModelGroupExporterView(ModelGroupExporterViewModel viewModel) : this()
        {
            ViewModel = viewModel;
            DataContext = viewModel;

            // Set up the close window action
            if (viewModel != null)
            {
                viewModel.CloseWindow = () => this.Close();
            }
        }

        /// <summary>
        /// Gets the result of the dialog (true if export was initiated, false if cancelled)
        /// </summary>
        public bool? ShowDialog(ModelGroupExporterViewModel viewModel)
        {
            ViewModel = viewModel;
            DataContext = viewModel;

            // Set up the close window action
            if (viewModel != null)
            {
                viewModel.CloseWindow = () => this.Close();
            }

            return ShowDialog();
        }
    }
}
