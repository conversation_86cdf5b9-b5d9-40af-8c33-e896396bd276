﻿using Autodesk.Revit.DB;

namespace GEN.ModelGroupExporter.Models
{
    /// <summary>
    /// Represents level information for export operations
    /// </summary>
    public class LevelModel
    {
        /// <summary>
        /// The original Revit Level element
        /// </summary>
        public Autodesk.Revit.DB.Level RevitLevel { get; set; }

        /// <summary>
        /// Unique identifier for the level
        /// </summary>
        public ElementId Id { get; set; }

        /// <summary>
        /// Name of the level
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Elevation of the level in project units
        /// </summary>
        public double Elevation { get; set; }

        /// <summary>
        /// Whether this level is structural
        /// </summary>
        public bool IsStructural { get; set; }

        /// <summary>
        /// Whether this level is a building story
        /// </summary>
        public bool IsBuildingStory { get; set; }

        /// <summary>
        /// Level type (if applicable)
        /// </summary>
        public ElementId LevelTypeId { get; set; }

        public LevelModel()
        {
        }

        public LevelModel(Autodesk.Revit.DB.Level revitLevel)
        {
            RevitLevel = revitLevel;
            Id = revitLevel.Id;
            Name = revitLevel.Name;
            Elevation = revitLevel.Elevation;

            // Get level properties
            var structuralParam = revitLevel.get_Parameter(BuiltInParameter.LEVEL_IS_STRUCTURAL);
            IsStructural = structuralParam?.AsInteger() == 1;

            var buildingStoryParam = revitLevel.get_Parameter(BuiltInParameter.LEVEL_IS_BUILDING_STORY);
            IsBuildingStory = buildingStoryParam?.AsInteger() == 1;

            LevelTypeId = revitLevel.GetTypeId();
        }

        public override string ToString()
        {
            return $"{Name} (Elev: {Elevation:F2})";
        }

        public override bool Equals(object obj)
        {
            if (obj is LevelModel other)
            {
                return Name == other.Name &&
                       System.Math.Abs(Elevation - other.Elevation) < 0.001; // Tolerance for double comparison
            }
            return false;
        }

        public override int GetHashCode()
        {
            return (Name?.GetHashCode() ?? 0) ^ Elevation.GetHashCode();
        }
    }
}
